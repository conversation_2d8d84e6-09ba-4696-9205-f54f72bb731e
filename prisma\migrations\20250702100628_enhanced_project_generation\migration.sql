-- Create<PERSON><PERSON>
CREATE TYPE "ProjectStatus" AS ENUM ('DRAFT', 'GENERATING', 'COMPLETE', 'ERROR');

-- C<PERSON><PERSON>num
CREATE TYPE "FileType" AS ENUM ('FILE', 'FOLDER');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "FileStatus" AS ENUM ('PENDING', 'GENERATING', 'WRITING', 'COMPLETE', 'ERROR');

-- Create<PERSON>num
CREATE TYPE "PackageStatus" AS ENUM ('PENDING', 'DOWNLOADING', 'INSTALLING', 'COMPLETE', 'ERROR');

-- AlterTable
ALTER TABLE "Fragment" ADD COLUMN     "codeLines" INTEGER,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "features" JSONB,
ADD COLUMN     "fileCount" INTEGER,
ADD COLUMN     "generationTime" INTEGER,
ADD COLUMN     "imageContext" JSONB,
ADD COLUMN     "imageUrl" TEXT,
ADD COLUMN     "technologies" JSONB;

-- AlterTable
ALTER TABLE "Project" ADD COLUMN     "completedAt" TIMESTAMP(3),
ADD COLUMN     "description" TEXT,
ADD COLUMN     "estimatedTime" INTEGER,
ADD COLUMN     "features" JSONB,
ADD COLUMN     "status" "ProjectStatus" NOT NULL DEFAULT 'DRAFT',
ADD COLUMN     "technologies" JSONB,
ADD COLUMN     "templateId" TEXT;

-- CreateTable
CREATE TABLE "ProjectFile" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "type" "FileType" NOT NULL,
    "content" TEXT,
    "size" INTEGER,
    "status" "FileStatus" NOT NULL DEFAULT 'PENDING',
    "parentId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProjectFile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProjectPackage" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "description" TEXT,
    "size" TEXT,
    "status" "PackageStatus" NOT NULL DEFAULT 'PENDING',
    "dependencies" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProjectPackage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProjectFile_projectId_path_key" ON "ProjectFile"("projectId", "path");

-- CreateIndex
CREATE UNIQUE INDEX "ProjectPackage_projectId_name_key" ON "ProjectPackage"("projectId", "name");

-- AddForeignKey
ALTER TABLE "ProjectFile" ADD CONSTRAINT "ProjectFile_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectFile" ADD CONSTRAINT "ProjectFile_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "ProjectFile"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectPackage" ADD CONSTRAINT "ProjectPackage_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

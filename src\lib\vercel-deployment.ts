import { randomBytes } from 'crypto';

export interface VercelProject {
  id: string;
  name: string;
  accountId: string;
  createdAt: number;
  updatedAt: number;
}

export interface VercelDeployment {
  id: string;
  url: string;
  name: string;
  state: 'BUILDING' | 'ERROR' | 'INITIALIZING' | 'QUEUED' | 'READY' | 'CANCELED';
  type: 'LAMBDAS';
  createdAt: number;
  buildingAt?: number;
  readyAt?: number;
  alias?: string[];
}

export interface VercelFile {
  file: string;
  data: string;
}

export class VercelDeploymentService {
  private apiToken: string;
  private baseUrl = 'https://api.vercel.com';

  constructor() {
    this.apiToken = process.env.VERCEL_API_TOKEN!;
    if (!this.apiToken) {
      throw new Error('VERCEL_API_TOKEN environment variable is required');
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vercel API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  async createProject(name: string): Promise<VercelProject> {
    return this.makeRequest<VercelProject>('/v9/projects', {
      method: 'POST',
      body: JSON.stringify({
        name,
        framework: 'nextjs',
        buildCommand: 'npm run build',
        devCommand: 'npm run dev',
        installCommand: 'npm install',
        outputDirectory: '.next',
      }),
    });
  }

  async deployProject(
    projectName: string,
    files: Record<string, string>,
    projectId?: string
  ): Promise<VercelDeployment> {
    // Convert files to Vercel format
    const vercelFiles: VercelFile[] = Object.entries(files).map(([path, content]) => ({
      file: path,
      data: Buffer.from(content).toString('base64'),
    }));

    // Add package.json if not present
    if (!files['package.json']) {
      const packageJson = {
        name: projectName,
        version: '0.1.0',
        private: true,
        scripts: {
          dev: 'next dev',
          build: 'next build',
          start: 'next start',
          lint: 'next lint',
        },
        dependencies: {
          next: '15.3.4',
          react: '^19.0.0',
          'react-dom': '^19.0.0',
        },
        devDependencies: {
          '@types/node': '^20',
          '@types/react': '^19',
          '@types/react-dom': '^19',
          typescript: '^5',
        },
      };

      vercelFiles.push({
        file: 'package.json',
        data: Buffer.from(JSON.stringify(packageJson, null, 2)).toString('base64'),
      });
    }

    // Add next.config.js if not present
    if (!files['next.config.js'] && !files['next.config.mjs']) {
      const nextConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
}

module.exports = nextConfig`;

      vercelFiles.push({
        file: 'next.config.js',
        data: Buffer.from(nextConfig).toString('base64'),
      });
    }

    const deploymentData = {
      name: projectName,
      files: vercelFiles,
      projectSettings: {
        framework: 'nextjs',
        buildCommand: 'npm run build',
        devCommand: 'npm run dev',
        installCommand: 'npm install',
        outputDirectory: '.next',
      },
      target: 'production',
    };

    if (projectId) {
      deploymentData.projectSettings = {
        ...deploymentData.projectSettings,
        projectId,
      };
    }

    return this.makeRequest<VercelDeployment>('/v13/deployments', {
      method: 'POST',
      body: JSON.stringify(deploymentData),
    });
  }

  async getDeployment(deploymentId: string): Promise<VercelDeployment> {
    return this.makeRequest<VercelDeployment>(`/v13/deployments/${deploymentId}`);
  }

  async getProject(projectId: string): Promise<VercelProject> {
    return this.makeRequest<VercelProject>(`/v9/projects/${projectId}`);
  }

  generateProjectName(fragmentTitle: string): string {
    // Create a URL-safe project name
    const sanitized = fragmentTitle
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
    
    // Add random suffix to ensure uniqueness
    const randomSuffix = randomBytes(4).toString('hex');
    return `${sanitized}-${randomSuffix}`.substring(0, 50);
  }

  getDeploymentUrl(deployment: VercelDeployment): string {
    if (deployment.alias && deployment.alias.length > 0) {
      return `https://${deployment.alias[0]}`;
    }
    return `https://${deployment.url}`;
  }
}

export const vercelService = new VercelDeploymentService();

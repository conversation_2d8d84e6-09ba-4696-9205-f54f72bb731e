import { randomBytes } from 'crypto';
import { VercelBuildPipeline } from './vercel-build-pipeline';

export interface VercelProject {
  id: string;
  name: string;
  accountId: string;
  createdAt: number;
  updatedAt: number;
}

export interface VercelDeployment {
  id: string;
  url: string;
  name: string;
  state: 'BUILDING' | 'ERROR' | 'INITIALIZING' | 'QUEUED' | 'READY' | 'CANCELED';
  type: 'LAMBDAS';
  createdAt: number;
  buildingAt?: number;
  readyAt?: number;
  alias?: string[];
}

export interface VercelFile {
  file: string;
  data: string;
  encoding?: 'base64' | 'utf8';
}

export class VercelDeploymentService {
  private apiToken: string;
  private baseUrl = 'https://api.vercel.com';

  constructor() {
    this.apiToken = process.env.VERCEL_API_TOKEN!;
    if (!this.apiToken) {
      throw new Error('VERCEL_API_TOKEN environment variable is required');
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vercel API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  async createProject(name: string): Promise<VercelProject> {
    return this.makeRequest<VercelProject>('/v9/projects', {
      method: 'POST',
      body: JSON.stringify({
        name,
        framework: 'nextjs',
        buildCommand: 'npm run build',
        devCommand: 'npm run dev',
        installCommand: 'npm install',
        outputDirectory: '.next',
      }),
    });
  }

  async deployProject(
    projectName: string,
    files: Record<string, string>,
    projectId?: string
  ): Promise<VercelDeployment> {
    console.log('Starting build pipeline for project:', projectName);
    console.log('Input files:', Object.keys(files));

    // Use the build pipeline to prepare all necessary files
    const vercelFiles = VercelBuildPipeline.prepareProjectFiles({
      projectName,
      files,
      dependencies: this.extractDependencies(files),
      devDependencies: this.extractDevDependencies(files)
    });

    console.log('Build pipeline generated files:', vercelFiles.map(f => f.file));

    const deploymentData: any = {
      name: projectName,
      files: vercelFiles,
      target: 'production',
      // Always include projectSettings as required by Vercel API
      projectSettings: {
        framework: 'nextjs',
        buildCommand: 'npm run build',
        devCommand: 'npm run dev',
        installCommand: 'npm install',
        outputDirectory: '.next',
        rootDirectory: null,
      },
    };

    // Add project ID if we have one (for redeployments)
    if (projectId) {
      deploymentData.projectSettings.projectId = projectId;
    }

    console.log('Deployment data being sent to Vercel:', JSON.stringify(deploymentData, null, 2));

    return this.makeRequest<VercelDeployment>('/v13/deployments', {
      method: 'POST',
      body: JSON.stringify(deploymentData),
    });
  }

  async getDeployment(deploymentId: string): Promise<VercelDeployment> {
    return this.makeRequest<VercelDeployment>(`/v13/deployments/${deploymentId}`);
  }

  async getProject(projectId: string): Promise<VercelProject> {
    return this.makeRequest<VercelProject>(`/v9/projects/${projectId}`);
  }

  generateProjectName(fragmentTitle: string): string {
    // Create a URL-safe project name
    const sanitized = fragmentTitle
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
    
    // Add random suffix to ensure uniqueness
    const randomSuffix = randomBytes(4).toString('hex');
    return `${sanitized}-${randomSuffix}`.substring(0, 50);
  }

  getDeploymentUrl(deployment: VercelDeployment): string {
    if (deployment.alias && deployment.alias.length > 0) {
      return `https://${deployment.alias[0]}`;
    }
    return `https://${deployment.url}`;
  }

  private extractDependencies(files: Record<string, string>): Record<string, string> {
    const dependencies: Record<string, string> = {};

    // Analyze files to determine required dependencies
    const allContent = Object.values(files).join('\n');

    // Check for common libraries
    if (allContent.includes('lucide-react')) {
      dependencies['lucide-react'] = '^0.263.1';
    }

    if (allContent.includes('framer-motion')) {
      dependencies['framer-motion'] = '^10.16.4';
    }

    if (allContent.includes('clsx') || allContent.includes('cn(')) {
      dependencies['clsx'] = '^2.0.0';
      dependencies['tailwind-merge'] = '^1.14.0';
    }

    return dependencies;
  }

  private extractDevDependencies(files: Record<string, string>): Record<string, string> {
    const devDependencies: Record<string, string> = {};

    // Check if Tailwind is needed
    const allContent = Object.values(files).join('\n');
    const tailwindPattern = /class(Name)?=["'][^"']*\b(bg-|text-|p-|m-|w-|h-|flex|grid|border|rounded)/;

    if (tailwindPattern.test(allContent)) {
      devDependencies['tailwindcss'] = '^3.4.0';
      devDependencies['autoprefixer'] = '^10.4.0';
      devDependencies['postcss'] = '^8.4.0';
    }

    return devDependencies;
  }
}

export const vercelService = new VercelDeploymentService();

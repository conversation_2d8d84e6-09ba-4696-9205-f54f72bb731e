/*
  Warnings:

  - You are about to drop the column `codeLines` on the `Fragment` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `Fragment` table. All the data in the column will be lost.
  - You are about to drop the column `features` on the `Fragment` table. All the data in the column will be lost.
  - You are about to drop the column `fileCount` on the `Fragment` table. All the data in the column will be lost.
  - You are about to drop the column `generationTime` on the `Fragment` table. All the data in the column will be lost.
  - You are about to drop the column `imageContext` on the `Fragment` table. All the data in the column will be lost.
  - You are about to drop the column `imageUrl` on the `Fragment` table. All the data in the column will be lost.
  - You are about to drop the column `technologies` on the `Fragment` table. All the data in the column will be lost.
  - You are about to drop the column `completedAt` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `estimatedTime` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `features` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `technologies` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `templateId` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the `ProjectFile` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `ProjectPackage` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "DeploymentStatus" AS ENUM ('NOT_DEPLOYED', 'DEPLOYING', 'DEPLOYED', 'FAILED');

-- CreateEnum
CREATE TYPE "ProgressStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'ERROR');

-- CreateEnum
CREATE TYPE "ProgressType" AS ENUM ('SETUP', 'DEPENDENCY', 'FILE', 'CONFIG', 'OPTIMIZATION');

-- DropForeignKey
ALTER TABLE "ProjectFile" DROP CONSTRAINT "ProjectFile_parentId_fkey";

-- DropForeignKey
ALTER TABLE "ProjectFile" DROP CONSTRAINT "ProjectFile_projectId_fkey";

-- DropForeignKey
ALTER TABLE "ProjectPackage" DROP CONSTRAINT "ProjectPackage_projectId_fkey";

-- AlterTable
ALTER TABLE "Fragment" DROP COLUMN "codeLines",
DROP COLUMN "description",
DROP COLUMN "features",
DROP COLUMN "fileCount",
DROP COLUMN "generationTime",
DROP COLUMN "imageContext",
DROP COLUMN "imageUrl",
DROP COLUMN "technologies",
ADD COLUMN     "deployedAt" TIMESTAMP(3),
ADD COLUMN     "deploymentError" TEXT,
ADD COLUMN     "deploymentStatus" "DeploymentStatus" NOT NULL DEFAULT 'NOT_DEPLOYED',
ADD COLUMN     "deploymentUrl" TEXT,
ADD COLUMN     "vercelDeploymentId" TEXT,
ADD COLUMN     "vercelProjectId" TEXT;

-- AlterTable
ALTER TABLE "Project" DROP COLUMN "completedAt",
DROP COLUMN "description",
DROP COLUMN "estimatedTime",
DROP COLUMN "features",
DROP COLUMN "status",
DROP COLUMN "technologies",
DROP COLUMN "templateId";

-- DropTable
DROP TABLE "ProjectFile";

-- DropTable
DROP TABLE "ProjectPackage";

-- DropEnum
DROP TYPE "FileStatus";

-- DropEnum
DROP TYPE "FileType";

-- DropEnum
DROP TYPE "PackageStatus";

-- DropEnum
DROP TYPE "ProjectStatus";

-- CreateTable
CREATE TABLE "GenerationProgress" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "fileName" TEXT,
    "fileContent" TEXT,
    "status" "ProgressStatus" NOT NULL DEFAULT 'PENDING',
    "type" "ProgressType" NOT NULL DEFAULT 'FILE',
    "order" INTEGER NOT NULL DEFAULT 0,
    "duration" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "GenerationProgress_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "GenerationProgress_projectId_order_idx" ON "GenerationProgress"("projectId", "order");

-- AddForeignKey
ALTER TABLE "GenerationProgress" ADD CONSTRAINT "GenerationProgress_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

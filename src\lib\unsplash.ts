import { createApi } from 'unsplash-js';

// Unsplash API configuration
const unsplash = createApi({
  accessKey: '*******************************************',
});

// Image size options
export type ImageSize = 'small' | 'regular' | 'full' | 'thumb';

// Project context types for smart image selection
export type ProjectContext = 
  | 'netflix' | 'movie' | 'entertainment'
  | 'youtube' | 'video' | 'media'
  | 'spotify' | 'music' | 'audio'
  | 'store' | 'ecommerce' | 'shopping' | 'product'
  | 'airbnb' | 'travel' | 'property' | 'real-estate'
  | 'admin' | 'dashboard' | 'business' | 'professional'
  | 'kanban' | 'productivity' | 'office'
  | 'file-manager' | 'documents' | 'files'
  | 'restaurant' | 'food' | 'dining'
  | 'fitness' | 'gym' | 'health' | 'sports'
  | 'portfolio' | 'creative' | 'design'
  | 'blog' | 'writing' | 'content'
  | 'social' | 'community' | 'people'
  | 'tech' | 'coding' | 'development'
  | 'nature' | 'landscape' | 'outdoor'
  | 'abstract' | 'minimal' | 'geometric'
  | 'general';

// Context to search terms mapping
const contextSearchTerms: Record<ProjectContext, string[]> = {
  netflix: ['movie', 'cinema', 'film', 'entertainment', 'streaming'],
  movie: ['movie', 'cinema', 'film', 'entertainment', 'theater'],
  entertainment: ['entertainment', 'fun', 'leisure', 'activity'],
  youtube: ['video', 'content', 'creator', 'streaming', 'media'],
  video: ['video', 'media', 'content', 'streaming'],
  media: ['media', 'content', 'digital', 'streaming'],
  spotify: ['music', 'audio', 'sound', 'headphones', 'concert'],
  music: ['music', 'audio', 'sound', 'instrument', 'concert'],
  audio: ['audio', 'sound', 'music', 'headphones'],
  store: ['shopping', 'retail', 'commerce', 'products', 'market'],
  ecommerce: ['ecommerce', 'shopping', 'online store', 'retail'],
  shopping: ['shopping', 'retail', 'store', 'products', 'market'],
  product: ['product', 'item', 'goods', 'merchandise'],
  airbnb: ['travel', 'vacation', 'accommodation', 'house', 'apartment'],
  travel: ['travel', 'vacation', 'journey', 'destination', 'adventure'],
  property: ['house', 'home', 'apartment', 'real estate', 'building'],
  'real-estate': ['real estate', 'property', 'house', 'apartment'],
  admin: ['office', 'business', 'professional', 'corporate', 'workspace'],
  dashboard: ['analytics', 'data', 'charts', 'business', 'metrics'],
  business: ['business', 'corporate', 'professional', 'office'],
  professional: ['professional', 'business', 'corporate', 'office'],
  kanban: ['productivity', 'organization', 'planning', 'workflow'],
  productivity: ['productivity', 'organization', 'efficiency', 'workflow'],
  office: ['office', 'workspace', 'business', 'professional'],
  'file-manager': ['documents', 'files', 'organization', 'storage'],
  documents: ['documents', 'files', 'papers', 'office'],
  files: ['files', 'documents', 'storage', 'organization'],
  restaurant: ['food', 'restaurant', 'dining', 'cuisine', 'meal'],
  food: ['food', 'cuisine', 'meal', 'cooking', 'restaurant'],
  dining: ['dining', 'restaurant', 'food', 'meal'],
  fitness: ['fitness', 'gym', 'exercise', 'workout', 'health'],
  gym: ['gym', 'fitness', 'exercise', 'workout', 'training'],
  health: ['health', 'wellness', 'fitness', 'medical'],
  sports: ['sports', 'athletics', 'competition', 'fitness'],
  portfolio: ['portfolio', 'creative', 'design', 'art', 'professional'],
  creative: ['creative', 'art', 'design', 'artistic'],
  design: ['design', 'creative', 'art', 'aesthetic'],
  blog: ['writing', 'blog', 'content', 'journalism'],
  writing: ['writing', 'content', 'blog', 'journalism'],
  content: ['content', 'writing', 'media', 'information'],
  social: ['social', 'community', 'people', 'networking'],
  community: ['community', 'social', 'people', 'group'],
  people: ['people', 'social', 'community', 'human'],
  tech: ['technology', 'coding', 'development', 'computer'],
  coding: ['coding', 'programming', 'development', 'technology'],
  development: ['development', 'coding', 'programming', 'technology'],
  nature: ['nature', 'landscape', 'outdoor', 'environment'],
  landscape: ['landscape', 'nature', 'scenery', 'outdoor'],
  outdoor: ['outdoor', 'nature', 'adventure', 'landscape'],
  abstract: ['abstract', 'minimal', 'geometric', 'pattern'],
  minimal: ['minimal', 'simple', 'clean', 'abstract'],
  geometric: ['geometric', 'pattern', 'abstract', 'design'],
  general: ['background', 'texture', 'pattern', 'abstract']
};

// Cache for storing fetched images
interface CachedImage {
  url: string;
  attribution: string;
  photographer?: string;
  timestamp: number;
}

const imageCache = new Map<string, CachedImage[]>();
const CACHE_DURATION = 1000 * 60 * 60; // 1 hour
const RATE_LIMIT_DELAY = 200; // 200ms between requests to respect rate limits

// Rate limiting
let lastRequestTime = 0;

async function rateLimitedRequest<T>(requestFn: () => Promise<T>): Promise<T> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY - timeSinceLastRequest));
  }
  
  lastRequestTime = Date.now();
  return requestFn();
}

// Get cached images or fetch new ones
async function getCachedImages(cacheKey: string, fetchFn: () => Promise<CachedImage[]>): Promise<CachedImage[]> {
  const cached = imageCache.get(cacheKey);
  
  if (cached && Date.now() - cached[0]?.timestamp < CACHE_DURATION) {
    return cached;
  }
  
  const fresh = await fetchFn();
  imageCache.set(cacheKey, fresh);
  return fresh;
}

/**
 * Fetch images from Unsplash based on context with enhanced error handling
 */
export async function getImagesByContext(
  context: ProjectContext,
  count: number = 10,
  size: ImageSize = 'regular'
): Promise<{ url: string; attribution: string; photographer?: string }[]> {
  const searchTerms = contextSearchTerms[context] || contextSearchTerms.general;
  const randomTerm = searchTerms[Math.floor(Math.random() * searchTerms.length)];

  const cacheKey = `${context}-${randomTerm}-${count}-${size}`;

  try {
    const images = await getCachedImages(cacheKey, async () => {
      const response = await rateLimitedRequest(() =>
        unsplash.search.getPhotos({
          query: randomTerm,
          page: 1,
          perPage: count,
          orientation: 'landscape'
        })
      );

      if (response.errors) {
        console.error('Unsplash API error:', response.errors);
        // Return fallback static URLs on API error
        return Array.from({ length: count }, (_, i) => ({
          url: generateUnsplashUrl(context, 400, 300, `fallback-${i}`),
          attribution: 'Photo from Unsplash',
          timestamp: Date.now()
        }));
      }

      return response.response?.results.map(photo => ({
        url: photo.urls[size],
        attribution: `Photo by ${photo.user.name} on Unsplash`,
        photographer: photo.user.username,
        timestamp: Date.now()
      })) || [];
    });

    return images.map(({ url, attribution, photographer }) => ({
      url,
      attribution,
      photographer
    }));
  } catch (error) {
    console.error('Error fetching images from Unsplash:', error);

    // Return fallback static URLs on any error
    return Array.from({ length: count }, (_, i) => ({
      url: generateUnsplashUrl(context, 400, 300, `error-fallback-${i}`),
      attribution: 'Photo from Unsplash',
    }));
  }
}

/**
 * Get a single random image for a specific context
 */
export async function getRandomImage(
  context: ProjectContext,
  size: ImageSize = 'regular'
): Promise<{ url: string; attribution: string } | null> {
  const images = await getImagesByContext(context, 1, size);
  return images[0] || null;
}

/**
 * Advanced context detection with scoring system
 */
interface ContextScore {
  context: ProjectContext;
  score: number;
}

const contextKeywords: Record<ProjectContext, string[]> = {
  netflix: ['netflix', 'movie', 'film', 'cinema', 'streaming', 'entertainment', 'series', 'show'],
  movie: ['movie', 'film', 'cinema', 'theater', 'entertainment', 'actor', 'director'],
  entertainment: ['entertainment', 'fun', 'leisure', 'activity', 'game', 'show'],
  youtube: ['youtube', 'video', 'content', 'creator', 'channel', 'subscribe', 'watch'],
  video: ['video', 'media', 'content', 'streaming', 'player', 'watch', 'clip'],
  media: ['media', 'content', 'digital', 'streaming', 'broadcast', 'news'],
  spotify: ['spotify', 'music', 'audio', 'song', 'playlist', 'artist', 'album'],
  music: ['music', 'audio', 'sound', 'song', 'artist', 'album', 'concert', 'band'],
  audio: ['audio', 'sound', 'music', 'podcast', 'voice', 'speaker'],
  store: ['store', 'shop', 'retail', 'commerce', 'buy', 'sell', 'market', 'purchase'],
  ecommerce: ['ecommerce', 'online store', 'shopping', 'cart', 'checkout', 'payment'],
  shopping: ['shopping', 'cart', 'buy', 'purchase', 'product', 'item', 'order'],
  product: ['product', 'item', 'goods', 'merchandise', 'catalog', 'inventory'],
  airbnb: ['airbnb', 'accommodation', 'booking', 'rental', 'host', 'guest', 'stay'],
  travel: ['travel', 'vacation', 'trip', 'journey', 'destination', 'tourism', 'flight'],
  property: ['property', 'real estate', 'house', 'apartment', 'home', 'listing'],
  'real-estate': ['real estate', 'property', 'house', 'apartment', 'rent', 'buy'],
  admin: ['admin', 'administration', 'management', 'control', 'settings', 'configure'],
  dashboard: ['dashboard', 'analytics', 'metrics', 'data', 'chart', 'graph', 'stats'],
  business: ['business', 'corporate', 'company', 'enterprise', 'professional'],
  professional: ['professional', 'business', 'corporate', 'work', 'career'],
  kanban: ['kanban', 'board', 'task', 'project', 'workflow', 'organize'],
  productivity: ['productivity', 'efficiency', 'organize', 'manage', 'workflow'],
  office: ['office', 'workspace', 'desk', 'meeting', 'team', 'work'],
  'file-manager': ['file manager', 'files', 'folder', 'directory', 'storage'],
  documents: ['document', 'file', 'paper', 'report', 'text', 'pdf'],
  files: ['file', 'folder', 'directory', 'storage', 'upload', 'download'],
  restaurant: ['restaurant', 'dining', 'menu', 'chef', 'cuisine', 'meal', 'food'],
  food: ['food', 'meal', 'recipe', 'cooking', 'kitchen', 'ingredient', 'dish'],
  dining: ['dining', 'restaurant', 'meal', 'table', 'service', 'waiter'],
  fitness: ['fitness', 'exercise', 'workout', 'training', 'health', 'strength'],
  gym: ['gym', 'fitness', 'exercise', 'workout', 'equipment', 'training'],
  health: ['health', 'wellness', 'medical', 'doctor', 'care', 'treatment'],
  sports: ['sport', 'athletic', 'competition', 'team', 'game', 'player'],
  portfolio: ['portfolio', 'showcase', 'work', 'project', 'gallery', 'creative'],
  creative: ['creative', 'art', 'design', 'artistic', 'imagination', 'innovation'],
  design: ['design', 'ui', 'ux', 'interface', 'layout', 'visual', 'graphic'],
  blog: ['blog', 'article', 'post', 'writing', 'author', 'publish'],
  writing: ['writing', 'content', 'text', 'article', 'author', 'editor'],
  content: ['content', 'information', 'text', 'media', 'publish', 'create'],
  social: ['social', 'network', 'friend', 'follow', 'share', 'community'],
  community: ['community', 'group', 'member', 'forum', 'discussion', 'social'],
  people: ['people', 'person', 'user', 'profile', 'contact', 'human'],
  tech: ['tech', 'technology', 'software', 'hardware', 'digital', 'innovation'],
  coding: ['code', 'programming', 'developer', 'software', 'script', 'algorithm'],
  development: ['development', 'developer', 'programming', 'software', 'build'],
  nature: ['nature', 'natural', 'environment', 'green', 'tree', 'plant'],
  landscape: ['landscape', 'scenery', 'view', 'mountain', 'valley', 'horizon'],
  outdoor: ['outdoor', 'outside', 'nature', 'adventure', 'hiking', 'camping'],
  abstract: ['abstract', 'pattern', 'geometric', 'minimal', 'modern', 'artistic'],
  minimal: ['minimal', 'simple', 'clean', 'basic', 'plain', 'elegant'],
  geometric: ['geometric', 'shape', 'pattern', 'symmetry', 'angle', 'form'],
  general: ['background', 'texture', 'pattern', 'image', 'photo', 'picture']
};

/**
 * Detect project context from prompt text using advanced scoring
 */
export function detectProjectContext(prompt: string): ProjectContext {
  const lowerPrompt = prompt.toLowerCase();
  const scores: ContextScore[] = [];

  // Calculate scores for each context
  Object.entries(contextKeywords).forEach(([context, keywords]) => {
    let score = 0;
    keywords.forEach(keyword => {
      if (lowerPrompt.includes(keyword)) {
        // Give higher scores for exact matches and longer keywords
        score += keyword.length * (lowerPrompt.split(keyword).length - 1);
      }
    });

    if (score > 0) {
      scores.push({ context: context as ProjectContext, score });
    }
  });

  // Sort by score and return the highest scoring context
  scores.sort((a, b) => b.score - a.score);

  return scores.length > 0 ? scores[0].context : 'general';
}

/**
 * Get multiple context suggestions for complex prompts
 */
export function getContextSuggestions(prompt: string, limit: number = 3): ProjectContext[] {
  const lowerPrompt = prompt.toLowerCase();
  const scores: ContextScore[] = [];

  Object.entries(contextKeywords).forEach(([context, keywords]) => {
    let score = 0;
    keywords.forEach(keyword => {
      if (lowerPrompt.includes(keyword)) {
        score += keyword.length * (lowerPrompt.split(keyword).length - 1);
      }
    });

    if (score > 0) {
      scores.push({ context: context as ProjectContext, score });
    }
  });

  return scores
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.context);
}

/**
 * Generate Unsplash URL with specific dimensions
 */
export function generateUnsplashUrl(
  context: ProjectContext,
  width: number,
  height: number,
  seed?: string
): string {
  const searchTerms = contextSearchTerms[context] || contextSearchTerms.general;
  const term = searchTerms[0]; // Use first term for consistency
  const seedParam = seed ? `&sig=${seed}` : '';
  
  return `https://source.unsplash.com/${width}x${height}/?${term}${seedParam}`;
}

/**
 * Smart image selection that tries multiple contexts for better variety
 */
export async function getSmartImages(
  prompt: string,
  count: number,
  size: ImageSize = 'regular'
): Promise<{ url: string; attribution: string; context: ProjectContext }[]> {
  const contexts = getContextSuggestions(prompt, 3);
  const images: { url: string; attribution: string; context: ProjectContext }[] = [];

  // Try to get images from multiple contexts for variety
  for (const context of contexts) {
    if (images.length >= count) break;

    const remainingCount = count - images.length;
    const contextImages = await getImagesByContext(context, remainingCount, size);

    contextImages.forEach(img => {
      images.push({ ...img, context });
    });
  }

  // If we still need more images, fill with the primary context
  if (images.length < count && contexts.length > 0) {
    const primaryContext = contexts[0];
    const additionalImages = await getImagesByContext(
      primaryContext,
      count - images.length,
      size
    );

    additionalImages.forEach(img => {
      images.push({ ...img, context: primaryContext });
    });
  }

  return images.slice(0, count);
}

/**
 * Get themed image collections for specific project types
 */
export async function getThemedImageCollection(
  projectType: 'netflix' | 'youtube' | 'spotify' | 'store' | 'airbnb' | 'restaurant' | 'fitness' | 'portfolio',
  size: ImageSize = 'regular'
): Promise<{
  hero: { url: string; attribution: string };
  gallery: { url: string; attribution: string }[];
  thumbnails: { url: string; attribution: string }[];
}> {
  const contextMap = {
    netflix: ['movie', 'entertainment', 'cinema'] as ProjectContext[],
    youtube: ['video', 'media', 'content'] as ProjectContext[],
    spotify: ['music', 'audio', 'concert'] as ProjectContext[],
    store: ['product', 'shopping', 'retail'] as ProjectContext[],
    airbnb: ['travel', 'property', 'accommodation'] as ProjectContext[],
    restaurant: ['food', 'dining', 'restaurant'] as ProjectContext[],
    fitness: ['fitness', 'gym', 'health'] as ProjectContext[],
    portfolio: ['creative', 'design', 'professional'] as ProjectContext[]
  };

  const contexts = contextMap[projectType];

  const [heroImages, galleryImages, thumbnailImages] = await Promise.all([
    getImagesByContext(contexts[0], 1, size),
    getImagesByContext(contexts[1] || contexts[0], 6, size),
    getImagesByContext(contexts[2] || contexts[0], 12, 'thumb' as ImageSize)
  ]);

  return {
    hero: heroImages[0] || { url: '', attribution: '' },
    gallery: galleryImages,
    thumbnails: thumbnailImages
  };
}

/**
 * Create attribution text for Unsplash images
 */
export function createAttribution(photographer?: string): string {
  if (photographer) {
    return `Photo by ${photographer} on Unsplash`;
  }
  return 'Photo from Unsplash';
}

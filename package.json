{"name": "vibe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@clerk/nextjs": "^6.23.1", "@clerk/themes": "^2.2.52", "@e2b/code-interpreter": "^1.5.1", "@hookform/resolvers": "^5.1.1", "@inngest/agent-kit": "^0.8.3", "@prisma/client": "^6.10.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.2", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "inngest": "^3.39.2", "input-otp": "^1.4.2", "lucide-react": "^0.523.0", "next": "15.3.4", "next-themes": "^0.4.6", "prismjs": "^1.30.0", "random-word-slugs": "^0.1.7", "rate-limiter-flexible": "^7.1.1", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.58.1", "react-resizable-panels": "^3.0.3", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "recharts": "^3.0.0", "server-only": "^0.0.1", "sonner": "^2.0.5", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "unsplash-js": "^7.0.19", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prisma": "^6.10.1", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/db';
import { vercelService } from '@/lib/vercel-deployment';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get fragment ID from query params
    const { searchParams } = new URL(request.url);
    const fragmentId = searchParams.get('fragmentId');
    
    if (!fragmentId) {
      return NextResponse.json({ error: 'Fragment ID is required' }, { status: 400 });
    }

    // Get the fragment
    const fragment = await prisma.fragment.findUnique({
      where: { id: fragmentId },
      include: {
        message: {
          include: {
            project: true,
          },
        },
      },
    });

    if (!fragment) {
      return NextResponse.json({ error: 'Fragment not found' }, { status: 404 });
    }

    // Check if user owns this fragment
    if (fragment.message.project.userId !== userId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // If deployment is in progress, check Vercel status
    if (fragment.deploymentStatus === 'DEPLOYING' && fragment.vercelDeploymentId) {
      try {
        const deployment = await vercelService.getDeployment(fragment.vercelDeploymentId);
        
        // Update local status based on Vercel status
        if (deployment.state === 'READY') {
          const updatedFragment = await prisma.fragment.update({
            where: { id: fragmentId },
            data: {
              deploymentStatus: 'DEPLOYED',
              deployedAt: new Date(),
              deploymentUrl: vercelService.getDeploymentUrl(deployment),
            },
          });
          return NextResponse.json(updatedFragment);
        } else if (deployment.state === 'ERROR' || deployment.state === 'CANCELED') {
          const updatedFragment = await prisma.fragment.update({
            where: { id: fragmentId },
            data: {
              deploymentStatus: 'FAILED',
              deploymentError: `Deployment ${deployment.state.toLowerCase()}`,
            },
          });
          return NextResponse.json(updatedFragment);
        }
      } catch (error) {
        console.error('Failed to check Vercel deployment status:', error);
      }
    }

    return NextResponse.json(fragment);
  } catch (error) {
    console.error('Deployment status API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
    }, { status: 500 });
  }
}

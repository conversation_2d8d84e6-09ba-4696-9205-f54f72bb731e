/**
 * Unsplash Attribution and Error Handling Components
 * Ensures compliance with Unsplash API guidelines and provides graceful fallbacks
 */

import React from 'react';

// Attribution component for Unsplash images
export const UnsplashAttribution = ({ 
  photographer, 
  className = "text-xs text-gray-400 mt-1" 
}: { 
  photographer?: string; 
  className?: string; 
}) => {
  if (photographer) {
    return (
      <div className={className}>
        Photo by{' '}
        <a 
          href={`https://unsplash.com/@${photographer}?utm_source=vibe&utm_medium=referral`}
          target="_blank"
          rel="noopener noreferrer"
          className="hover:underline"
        >
          {photographer}
        </a>
        {' '}on{' '}
        <a 
          href="https://unsplash.com/?utm_source=vibe&utm_medium=referral"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:underline"
        >
          Unsplash
        </a>
      </div>
    );
  }
  
  return (
    <div className={className}>
      <a 
        href="https://unsplash.com/?utm_source=vibe&utm_medium=referral"
        target="_blank"
        rel="noopener noreferrer"
        className="hover:underline"
      >
        Photo from Unsplash
      </a>
    </div>
  );
};

// Error boundary for image loading failures
export class ImageErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Image loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="bg-gray-200 flex items-center justify-center text-gray-500 text-sm">
          Image unavailable
        </div>
      );
    }

    return this.props.children;
  }
}

// Enhanced image component with error handling and attribution
export const UnsplashImage = ({
  src,
  alt,
  photographer,
  width,
  height,
  className = "",
  fallbackClassName = "bg-gray-200 flex items-center justify-center text-gray-500",
  showAttribution = true,
  attributionClassName,
  onError,
  ...props
}: {
  src: string;
  alt: string;
  photographer?: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackClassName?: string;
  showAttribution?: boolean;
  attributionClassName?: string;
  onError?: () => void;
  [key: string]: any;
}) => {
  const [imageError, setImageError] = React.useState(false);
  const [imageLoaded, setImageLoaded] = React.useState(false);

  const handleImageError = () => {
    setImageError(true);
    onError?.();
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  if (imageError) {
    return (
      <div className="space-y-2">
        <div 
          className={`${fallbackClassName} ${className}`}
          style={{ width, height }}
        >
          <span>Image unavailable</span>
        </div>
        {showAttribution && (
          <UnsplashAttribution 
            photographer={photographer} 
            className={attributionClassName}
          />
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="relative">
        {!imageLoaded && (
          <div 
            className={`absolute inset-0 ${fallbackClassName} animate-pulse`}
            style={{ width, height }}
          >
            <span>Loading...</span>
          </div>
        )}
        <img
          src={src}
          alt={alt}
          width={width}
          height={height}
          className={`${className} ${!imageLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity`}
          onError={handleImageError}
          onLoad={handleImageLoad}
          {...props}
        />
      </div>
      {showAttribution && (
        <UnsplashAttribution 
          photographer={photographer} 
          className={attributionClassName}
        />
      )}
    </div>
  );
};

// Background image component with error handling
export const UnsplashBackgroundImage = ({
  src,
  children,
  photographer,
  className = "",
  fallbackClassName = "bg-gray-200",
  showAttribution = true,
  attributionClassName,
  onError,
  ...props
}: {
  src: string;
  children?: React.ReactNode;
  photographer?: string;
  className?: string;
  fallbackClassName?: string;
  showAttribution?: boolean;
  attributionClassName?: string;
  onError?: () => void;
  [key: string]: any;
}) => {
  const [imageError, setImageError] = React.useState(false);
  const [imageLoaded, setImageLoaded] = React.useState(false);

  React.useEffect(() => {
    const img = new Image();
    img.onload = () => setImageLoaded(true);
    img.onerror = () => {
      setImageError(true);
      onError?.();
    };
    img.src = src;
  }, [src, onError]);

  const backgroundStyle = imageLoaded && !imageError 
    ? { backgroundImage: `url(${src})`, backgroundSize: 'cover', backgroundPosition: 'center' }
    : {};

  return (
    <div className="space-y-2">
      <div 
        className={`${imageError || !imageLoaded ? fallbackClassName : ''} ${className}`}
        style={backgroundStyle}
        {...props}
      >
        {children}
      </div>
      {showAttribution && (
        <UnsplashAttribution 
          photographer={photographer} 
          className={attributionClassName}
        />
      )}
    </div>
  );
};

// Utility function to create fallback image URLs
export const createFallbackImage = (width: number, height: number, text?: string): string => {
  const encodedText = encodeURIComponent(text || 'Image');
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#e5e7eb"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="14" fill="#6b7280" text-anchor="middle" dy=".3em">
        ${encodedText}
      </text>
    </svg>
  `)}`;
};

// Hook for managing image loading states
export const useImageLoader = (src: string) => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(false);

  React.useEffect(() => {
    setLoading(true);
    setError(false);

    const img = new Image();
    img.onload = () => {
      setLoading(false);
    };
    img.onerror = () => {
      setLoading(false);
      setError(true);
    };
    img.src = src;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src]);

  return { loading, error };
};

// Gallery component with error handling
export const UnsplashGallery = ({
  images,
  className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
  imageClassName = "w-full h-64 object-cover rounded-lg",
  showAttribution = true,
  onImageError
}: {
  images: Array<{ src: string; alt: string; photographer?: string }>;
  className?: string;
  imageClassName?: string;
  showAttribution?: boolean;
  onImageError?: (index: number) => void;
}) => {
  return (
    <div className={className}>
      {images.map((image, index) => (
        <UnsplashImage
          key={index}
          src={image.src}
          alt={image.alt}
          photographer={image.photographer}
          className={imageClassName}
          showAttribution={showAttribution}
          onError={() => onImageError?.(index)}
        />
      ))}
    </div>
  );
};

// Error handling utilities
export const handleUnsplashError = (error: any, context: string = 'image loading') => {
  console.error(`Unsplash ${context} error:`, error);
  
  // Log to analytics or error reporting service if available
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', 'exception', {
      description: `Unsplash ${context} error: ${error.message}`,
      fatal: false
    });
  }
};

// Rate limit error detection
export const isRateLimitError = (error: any): boolean => {
  return error?.status === 403 || 
         error?.message?.includes('rate limit') ||
         error?.message?.includes('quota exceeded');
};

// Network error detection
export const isNetworkError = (error: any): boolean => {
  return error?.message?.includes('network') ||
         error?.message?.includes('fetch') ||
         error?.code === 'NETWORK_ERROR';
};

// Retry mechanism for failed requests
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Don't retry rate limit errors
      if (isRateLimitError(error)) {
        break;
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
};

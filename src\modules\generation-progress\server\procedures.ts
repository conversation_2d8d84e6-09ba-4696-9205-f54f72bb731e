import { createTRPCRouter, baseProcedure } from '@/trpc/init';
import { z } from 'zod';
import { prisma } from '@/lib/db';

export const generationProgressRouter = createTRPCRouter({
  // Get all progress steps for a project
  getProgress: baseProcedure
    .input(z.object({
      projectId: z.string(),
    }))
    .query(async ({ input }) => {
      const progress = await prisma.generationProgress.findMany({
        where: {
          projectId: input.projectId,
        },
        orderBy: {
          order: 'asc',
        },
      });

      return progress;
    }),

  // Get current active step
  getCurrentStep: baseProcedure
    .input(z.object({
      projectId: z.string(),
    }))
    .query(async ({ input }) => {
      const currentStep = await prisma.generationProgress.findFirst({
        where: {
          projectId: input.projectId,
          status: 'IN_PROGRESS',
        },
        orderBy: {
          order: 'asc',
        },
      });

      return currentStep;
    }),

  // Get progress statistics
  getProgressStats: baseProcedure
    .input(z.object({
      projectId: z.string(),
    }))
    .query(async ({ input }) => {
      const [total, completed, inProgress, errors] = await Promise.all([
        prisma.generationProgress.count({
          where: { projectId: input.projectId },
        }),
        prisma.generationProgress.count({
          where: { projectId: input.projectId, status: 'COMPLETED' },
        }),
        prisma.generationProgress.count({
          where: { projectId: input.projectId, status: 'IN_PROGRESS' },
        }),
        prisma.generationProgress.count({
          where: { projectId: input.projectId, status: 'ERROR' },
        }),
      ]);

      const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

      return {
        total,
        completed,
        inProgress,
        errors,
        percentage,
      };
    }),

  // Create or update progress step
  upsertProgress: baseProcedure
    .input(z.object({
      projectId: z.string(),
      title: z.string(),
      description: z.string(),
      fileName: z.string().optional(),
      fileContent: z.string().optional(),
      status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'ERROR']),
      type: z.enum(['SETUP', 'DEPENDENCY', 'FILE', 'CONFIG', 'OPTIMIZATION']),
      order: z.number(),
      duration: z.number().optional(),
    }))
    .mutation(async ({ input }) => {
      // Check if step already exists
      const existingStep = await prisma.generationProgress.findFirst({
        where: {
          projectId: input.projectId,
          fileName: input.fileName || undefined,
          title: input.title,
        },
      });

      if (existingStep) {
        // Update existing step
        return await prisma.generationProgress.update({
          where: { id: existingStep.id },
          data: {
            description: input.description,
            fileContent: input.fileContent,
            status: input.status,
            duration: input.duration,
            updatedAt: new Date(),
          },
        });
      } else {
        // Create new step
        return await prisma.generationProgress.create({
          data: {
            projectId: input.projectId,
            title: input.title,
            description: input.description,
            fileName: input.fileName,
            fileContent: input.fileContent,
            status: input.status,
            type: input.type,
            order: input.order,
            duration: input.duration,
          },
        });
      }
    }),

  // Clear all progress for a project (when starting new generation)
  clearProgress: baseProcedure
    .input(z.object({
      projectId: z.string(),
    }))
    .mutation(async ({ input }) => {
      await prisma.generationProgress.deleteMany({
        where: {
          projectId: input.projectId,
        },
      });

      return { success: true };
    }),

  // Get file content by fileName
  getFileContent: baseProcedure
    .input(z.object({
      projectId: z.string(),
      fileName: z.string(),
    }))
    .query(async ({ input }) => {
      const progress = await prisma.generationProgress.findFirst({
        where: {
          projectId: input.projectId,
          fileName: input.fileName,
          status: 'COMPLETED',
        },
        select: {
          fileContent: true,
          title: true,
          description: true,
        },
      });

      return progress;
    }),
});

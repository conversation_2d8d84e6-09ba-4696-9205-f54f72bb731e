// Test Vercel API connectivity
const fetch = require('node-fetch');

async function testVercelAPI() {
  const apiToken = process.env.VERCEL_API_TOKEN || '************************';
  
  if (!apiToken) {
    console.log('❌ No Vercel API token found');
    return false;
  }

  try {
    console.log('Testing Vercel API connectivity...');
    console.log('API Token (first 10 chars):', apiToken.substring(0, 10) + '...');
    
    // Test basic API access
    const response = await fetch('https://api.vercel.com/v2/user', {
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Vercel API is accessible');
      console.log('User:', data.user?.username || data.user?.email || 'Unknown');
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ Vercel API error:', response.status, errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ Vercel API test failed:', error.message);
    return false;
  }
}

testVercelAPI();

import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { vercelService } from '@/lib/vercel-deployment';
import { prisma } from '@/lib/db';

export const deploymentRouter = createTRPCRouter({
  deploy: protectedProcedure
    .input(
      z.object({
        fragmentId: z.string(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Get the fragment with its files
        const fragment = await prisma.fragment.findUnique({
          where: { id: input.fragmentId },
          include: {
            message: {
              include: {
                project: true,
              },
            },
          },
        });

        if (!fragment) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Fragment not found',
          });
        }

        // Check if user owns this fragment
        if (fragment.message.project.userId !== ctx.auth.userId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to deploy this fragment',
          });
        }

        // Check if already deploying
        if (fragment.deploymentStatus === 'DEPLOYING') {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Fragment is already being deployed',
          });
        }

        // Update status to deploying
        await prisma.fragment.update({
          where: { id: input.fragmentId },
          data: {
            deploymentStatus: 'DEPLOYING',
            deploymentError: null,
          },
        });

        try {
          // Generate project name
          const projectName = vercelService.generateProjectName(fragment.title);

          // Deploy to Vercel
          const deployment = await vercelService.deployProject(
            projectName,
            fragment.files as Record<string, string>,
            fragment.vercelProjectId || undefined
          );

          // Get deployment URL
          const deploymentUrl = vercelService.getDeploymentUrl(deployment);

          // Update fragment with deployment info
          const updatedFragment = await prisma.fragment.update({
            where: { id: input.fragmentId },
            data: {
              deploymentStatus: deployment.state === 'READY' ? 'DEPLOYED' : 'DEPLOYING',
              deploymentUrl,
              vercelDeploymentId: deployment.id,
              deployedAt: deployment.state === 'READY' ? new Date() : null,
            },
          });

          return {
            success: true,
            deploymentUrl,
            deploymentId: deployment.id,
            status: deployment.state,
            fragment: updatedFragment,
          };
        } catch (deploymentError) {
          // Update fragment with error status
          await prisma.fragment.update({
            where: { id: input.fragmentId },
            data: {
              deploymentStatus: 'FAILED',
              deploymentError: deploymentError instanceof Error ? deploymentError.message : 'Unknown deployment error',
            },
          });

          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: `Deployment failed: ${deploymentError instanceof Error ? deploymentError.message : 'Unknown error'}`,
          });
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to deploy fragment',
        });
      }
    }),

  getStatus: protectedProcedure
    .input(
      z.object({
        fragmentId: z.string(),
      })
    )
    .query(async ({ input, ctx }) => {
      const fragment = await prisma.fragment.findUnique({
        where: { id: input.fragmentId },
        include: {
          message: {
            include: {
              project: true,
            },
          },
        },
      });

      if (!fragment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Fragment not found',
        });
      }

      // Check if user owns this fragment
      if (fragment.message.project.userId !== ctx.auth.userId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to view this fragment',
        });
      }

      // If deployment is in progress, check Vercel status
      if (fragment.deploymentStatus === 'DEPLOYING' && fragment.vercelDeploymentId) {
        try {
          const deployment = await vercelService.getDeployment(fragment.vercelDeploymentId);
          
          // Update local status based on Vercel status
          if (deployment.state === 'READY') {
            const updatedFragment = await prisma.fragment.update({
              where: { id: input.fragmentId },
              data: {
                deploymentStatus: 'DEPLOYED',
                deployedAt: new Date(),
                deploymentUrl: vercelService.getDeploymentUrl(deployment),
              },
            });
            return updatedFragment;
          } else if (deployment.state === 'ERROR' || deployment.state === 'CANCELED') {
            const updatedFragment = await prisma.fragment.update({
              where: { id: input.fragmentId },
              data: {
                deploymentStatus: 'FAILED',
                deploymentError: `Deployment ${deployment.state.toLowerCase()}`,
              },
            });
            return updatedFragment;
          }
        } catch (error) {
          console.error('Failed to check Vercel deployment status:', error);
        }
      }

      return fragment;
    }),

  redeploy: protectedProcedure
    .input(
      z.object({
        fragmentId: z.string(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      // Reset deployment status and redeploy
      await prisma.fragment.update({
        where: { id: input.fragmentId },
        data: {
          deploymentStatus: 'NOT_DEPLOYED',
          deploymentError: null,
          vercelDeploymentId: null,
          deploymentUrl: null,
          deployedAt: null,
        },
      });

      // Call deploy procedure
      return deploymentRouter.createCaller(ctx).deploy({ fragmentId: input.fragmentId });
    }),
});

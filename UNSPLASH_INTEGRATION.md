# Unsplash Integration for Vibe AI Code Generator

## Overview

This integration replaces generic placeholder images with contextually relevant Unsplash photos in your AI-generated projects. The system automatically detects project types and selects appropriate images based on context.

## Features

- 🎯 **Context-Aware Selection**: Automatically detects project type (Netflix, YouTube, Spotify, etc.) and selects relevant images
- 🚀 **Smart Caching**: Reduces API calls with intelligent caching system
- ⚡ **Rate Limiting**: Respects Unsplash API limits (5000 requests/hour)
- 🛡️ **Error Handling**: Graceful fallbacks when API fails
- 📱 **Responsive Images**: Multiple size options for different use cases
- ✅ **Proper Attribution**: Compliant with Unsplash guidelines
- 🎨 **25+ Project Types**: Supports wide variety of project contexts

## Quick Start

### 1. API Configuration

Your Unsplash API keys are already configured:
- Access Key: `ZwKg_265ypGCK19tz1aBWBWYWxmFOAzHcLOgTIw8jzU`
- Secret Key: `c_0iAuikS4JKIbmqGTrmmeOia4xpGU_H7SabbHyn60M`

### 2. Basic Usage

```typescript
import { getContextualImageUrl, getProjectSpecificImages } from '@/lib/image-helper';

// Context-aware image for any prompt
const imageUrl = getContextualImageUrl("Build a Netflix clone", 'card');

// Project-specific images
const movieThumbnails = getProjectSpecificImages.netflix.movieCards(6);
const albumCovers = getProjectSpecificImages.spotify.albumCovers(8);
```

### 3. React Components

```tsx
import { UnsplashImage } from '@/lib/unsplash-attribution';

<UnsplashImage
  src={getContextualImageUrl(prompt, 'hero')}
  alt="Hero image"
  className="w-full h-64 object-cover"
  showAttribution={true}
/>
```

## Supported Project Types

| Project Type | Context Keywords | Example Images |
|--------------|------------------|----------------|
| Netflix Clone | movie, film, cinema | Movie posters, entertainment |
| YouTube Clone | video, content, media | Video thumbnails, creators |
| Spotify Clone | music, audio, album | Album covers, concerts |
| E-commerce | store, shopping, product | Products, retail spaces |
| Airbnb Clone | travel, property, accommodation | Properties, destinations |
| Restaurant | food, dining, cuisine | Food photos, restaurant ambiance |
| Fitness App | fitness, gym, workout | Exercise, equipment, health |
| Portfolio | creative, design, professional | Creative work, professional |
| Admin Dashboard | business, office, professional | Office spaces, team photos |

## Image Dimensions

```typescript
const IMAGE_DIMENSIONS = {
  hero: { width: 1200, height: 600 },    // Hero banners
  card: { width: 400, height: 300 },     // Standard cards
  thumbnail: { width: 200, height: 150 }, // Small previews
  avatar: { width: 100, height: 100 },   // Profile images
  banner: { width: 1000, height: 300 },  // Section banners
  square: { width: 400, height: 400 },   // Square images
  portrait: { width: 300, height: 400 }, // Vertical images
  wide: { width: 800, height: 400 },     // Wide images
  small: { width: 300, height: 200 },    // Small images
  medium: { width: 600, height: 400 },   // Medium images
  large: { width: 900, height: 600 }     // Large images
};
```

## Advanced Usage

### Multiple Images with Variety

```typescript
import { getSmartImageUrls } from '@/lib/image-helper';

// Get images from multiple related contexts for variety
const images = await getSmartImageUrls("Build a travel app", 6, 'card');
```

### Complete Project Image Sets

```typescript
import { getProjectImageSet } from '@/lib/image-helper';

const imageSet = await getProjectImageSet('netflix');
// Returns: { hero, gallery, thumbnails }
```

### Error Handling

```tsx
<UnsplashImage
  src={imageUrl}
  alt="Description"
  className="w-full h-48 object-cover"
  fallbackClassName="w-full h-48 bg-gray-200 flex items-center justify-center"
  onError={() => console.log('Image failed to load')}
/>
```

## AI Agent Integration

The AI agent automatically uses Unsplash images when generating projects. The prompt system has been updated to:

1. **Import image helpers**: `import { getProjectSpecificImages } from '@/lib/image-helper'`
2. **Use context-specific functions**: Based on project type detected
3. **Include proper attribution**: Automatic attribution compliance
4. **Handle errors gracefully**: Fallback to static URLs if API fails

### Example Generated Code

```tsx
// Netflix clone with Unsplash integration
const NetflixHomepage = () => {
  const heroImage = getProjectSpecificImages.netflix.hero();
  const movieCards = getProjectSpecificImages.netflix.movieCards(12);
  
  return (
    <div className="bg-black text-white">
      <div 
        className="h-96 bg-cover bg-center"
        style={{ backgroundImage: `url(${heroImage})` }}
      >
        <div className="h-full bg-gradient-to-t from-black/80 to-transparent" />
      </div>
      
      <div className="grid grid-cols-6 gap-4 p-8">
        {movieCards.map((image, index) => (
          <img 
            key={index}
            src={image} 
            alt={`Movie ${index + 1}`}
            className="w-full h-32 object-cover rounded"
          />
        ))}
      </div>
      
      <div className="text-xs text-gray-400 p-4">
        Photos from Unsplash
      </div>
    </div>
  );
};
```

## Performance Optimizations

- **Caching**: Images cached for 1 hour to reduce API calls
- **Rate Limiting**: Built-in rate limiting respects API quotas
- **Lazy Loading**: Use with React lazy loading for better performance
- **Responsive Images**: Multiple sizes available for different viewports
- **Fallback URLs**: Static Unsplash URLs when API unavailable

## Attribution Compliance

All images include proper Unsplash attribution:

```tsx
// Automatic attribution
<UnsplashAttribution photographer="john_doe" />

// Renders: "Photo by john_doe on Unsplash" with proper links
```

## Error Handling & Fallbacks

1. **API Errors**: Falls back to static Unsplash URLs
2. **Rate Limits**: Cached responses prevent hitting limits
3. **Network Issues**: Graceful degradation to placeholders
4. **Invalid URLs**: Error boundaries catch and display fallbacks

## Testing

Run the integration test suite:

```tsx
import { FullIntegrationTest } from '@/lib/unsplash-integration-test';

// Renders comprehensive test suite showing:
// - Before/after comparisons
// - All project types
// - Error handling
// - Performance tests
```

## Files Created/Modified

### New Files
- `src/lib/unsplash.ts` - Core Unsplash service
- `src/lib/image-helper.ts` - Helper functions for AI agent
- `src/lib/unsplash-attribution.tsx` - Attribution components
- `src/lib/unsplash-examples.tsx` - Usage examples
- `src/lib/unsplash-integration-test.tsx` - Test suite

### Modified Files
- `src/prompt.ts` - Updated AI agent prompt to use Unsplash
- `src/modules/home/<USER>/components/constant.tsx` - Updated templates

## API Usage & Limits

- **Rate Limit**: 5000 requests/hour
- **Current Usage**: Cached responses minimize API calls
- **Monitoring**: Errors logged for tracking usage
- **Fallbacks**: Static URLs when limits exceeded

## Next Steps

1. **Monitor Usage**: Track API usage in production
2. **Optimize Caching**: Adjust cache duration based on usage patterns
3. **Add More Contexts**: Expand context detection for new project types
4. **Performance Metrics**: Monitor image loading performance
5. **User Feedback**: Collect feedback on image relevance

## Support

For issues or questions about the Unsplash integration:
1. Check error logs for API issues
2. Verify API keys are valid
3. Test with the integration test suite
4. Review Unsplash API documentation for updates

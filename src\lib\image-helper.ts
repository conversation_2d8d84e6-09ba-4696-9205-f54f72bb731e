import {
  detectProjectContext,
  generateUnsplashUrl,
  getContextSuggestions,
  getSmartImages,
  getThemedImageCollection,
  type ProjectContext,
  type ImageSize
} from './unsplash';

/**
 * Image helper utilities for the AI agent
 */

// Common image dimensions for different use cases
export const IMAGE_DIMENSIONS = {
  hero: { width: 1200, height: 600 },
  card: { width: 400, height: 300 },
  thumbnail: { width: 200, height: 150 },
  avatar: { width: 100, height: 100 },
  banner: { width: 1000, height: 300 },
  square: { width: 400, height: 400 },
  portrait: { width: 300, height: 400 },
  wide: { width: 800, height: 400 },
  small: { width: 300, height: 200 },
  medium: { width: 600, height: 400 },
  large: { width: 900, height: 600 }
} as const;

export type ImageDimension = keyof typeof IMAGE_DIMENSIONS;

/**
 * Generate a context-aware Unsplash image URL
 */
export function getContextualImageUrl(
  prompt: string,
  dimension: ImageDimension = 'card',
  seed?: string
): string {
  const context = detectProjectContext(prompt);
  const { width, height } = IMAGE_DIMENSIONS[dimension];
  return generateUnsplashUrl(context, width, height, seed);
}

/**
 * Generate multiple image URLs for galleries, grids, etc.
 */
export function getMultipleImageUrls(
  prompt: string,
  count: number,
  dimension: ImageDimension = 'card'
): string[] {
  const context = detectProjectContext(prompt);
  const { width, height } = IMAGE_DIMENSIONS[dimension];
  
  return Array.from({ length: count }, (_, index) => 
    generateUnsplashUrl(context, width, height, `seed-${index}`)
  );
}

/**
 * Get image URL with custom dimensions
 */
export function getCustomImageUrl(
  prompt: string,
  width: number,
  height: number,
  seed?: string
): string {
  const context = detectProjectContext(prompt);
  return generateUnsplashUrl(context, width, height, seed);
}

/**
 * Generate image URLs for specific project types
 */
export const getProjectSpecificImages = {
  netflix: {
    hero: () => generateUnsplashUrl('movie', 1200, 600, 'hero'),
    movieCards: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('movie', 300, 450, `movie-${i}`)
    ),
    thumbnails: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('entertainment', 200, 300, `thumb-${i}`)
    )
  },
  
  youtube: {
    hero: () => generateUnsplashUrl('video', 1200, 600, 'hero'),
    videoThumbnails: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('media', 320, 180, `video-${i}`)
    ),
    channelAvatars: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('people', 100, 100, `avatar-${i}`)
    )
  },
  
  spotify: {
    hero: () => generateUnsplashUrl('music', 1200, 600, 'hero'),
    albumCovers: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('music', 300, 300, `album-${i}`)
    ),
    playlistCovers: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('audio', 200, 200, `playlist-${i}`)
    )
  },
  
  store: {
    hero: () => generateUnsplashUrl('shopping', 1200, 600, 'hero'),
    productImages: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('product', 300, 300, `product-${i}`)
    ),
    categoryBanners: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('retail', 400, 200, `category-${i}`)
    )
  },
  
  airbnb: {
    hero: () => generateUnsplashUrl('travel', 1200, 600, 'hero'),
    propertyImages: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('property', 400, 300, `property-${i}`)
    ),
    destinations: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('landscape', 300, 200, `destination-${i}`)
    )
  },
  
  restaurant: {
    hero: () => generateUnsplashUrl('restaurant', 1200, 600, 'hero'),
    foodImages: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('food', 300, 300, `food-${i}`)
    ),
    ambiance: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('dining', 400, 300, `ambiance-${i}`)
    )
  },
  
  fitness: {
    hero: () => generateUnsplashUrl('fitness', 1200, 600, 'hero'),
    workoutImages: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('gym', 400, 300, `workout-${i}`)
    ),
    equipment: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('sports', 300, 300, `equipment-${i}`)
    )
  },
  
  portfolio: {
    hero: () => generateUnsplashUrl('creative', 1200, 600, 'hero'),
    projectImages: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('design', 400, 300, `project-${i}`)
    ),
    profileImage: () => generateUnsplashUrl('professional', 300, 300, 'profile')
  },
  
  admin: {
    hero: () => generateUnsplashUrl('business', 1200, 600, 'hero'),
    teamPhotos: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('professional', 100, 100, `team-${i}`)
    ),
    officeImages: (count: number) => Array.from({ length: count }, (_, i) => 
      generateUnsplashUrl('office', 400, 300, `office-${i}`)
    )
  }
};

/**
 * Generate image code snippet for React components
 */
export function generateImageComponent(
  prompt: string,
  dimension: ImageDimension = 'card',
  alt: string = 'Image',
  className: string = '',
  seed?: string
): string {
  const imageUrl = getContextualImageUrl(prompt, dimension, seed);
  const { width, height } = IMAGE_DIMENSIONS[dimension];
  
  return `<img 
    src="${imageUrl}" 
    alt="${alt}"
    width={${width}}
    height={${height}}
    className="${className}"
  />`;
}

/**
 * Generate background image CSS for divs
 */
export function generateBackgroundImageStyle(
  prompt: string,
  dimension: ImageDimension = 'card',
  seed?: string
): string {
  const imageUrl = getContextualImageUrl(prompt, dimension, seed);
  return `style={{ backgroundImage: 'url(${imageUrl})', backgroundSize: 'cover', backgroundPosition: 'center' }}`;
}

/**
 * Helper to create image arrays for mock data
 */
export function createImageArray(
  prompt: string,
  count: number,
  dimension: ImageDimension = 'card'
): Array<{ url: string; alt: string }> {
  const urls = getMultipleImageUrls(prompt, count, dimension);
  return urls.map((url, index) => ({
    url,
    alt: `Image ${index + 1}`
  }));
}

/**
 * Smart image selection with context awareness
 */
export async function getSmartImageUrls(
  prompt: string,
  count: number,
  dimension: ImageDimension = 'card'
): Promise<Array<{ url: string; alt: string; context: string }>> {
  try {
    const images = await getSmartImages(prompt, count, 'regular');
    const { width, height } = IMAGE_DIMENSIONS[dimension];

    return images.map((img, index) => ({
      url: img.url.replace(/w=\d+&h=\d+/, `w=${width}&h=${height}`),
      alt: `${img.context} image ${index + 1}`,
      context: img.context
    }));
  } catch (error) {
    console.error('Error fetching smart images:', error);
    // Fallback to static URLs
    return getMultipleImageUrls(prompt, count, dimension).map((url, index) => ({
      url,
      alt: `Image ${index + 1}`,
      context: detectProjectContext(prompt)
    }));
  }
}

/**
 * Get a complete themed image set for a project
 */
export async function getProjectImageSet(
  projectType: 'netflix' | 'youtube' | 'spotify' | 'store' | 'airbnb' | 'restaurant' | 'fitness' | 'portfolio'
): Promise<{
  hero: { url: string; alt: string };
  gallery: Array<{ url: string; alt: string }>;
  thumbnails: Array<{ url: string; alt: string }>;
}> {
  try {
    const collection = await getThemedImageCollection(projectType);

    return {
      hero: {
        url: collection.hero.url,
        alt: `${projectType} hero image`
      },
      gallery: collection.gallery.map((img, index) => ({
        url: img.url,
        alt: `${projectType} gallery image ${index + 1}`
      })),
      thumbnails: collection.thumbnails.map((img, index) => ({
        url: img.url,
        alt: `${projectType} thumbnail ${index + 1}`
      }))
    };
  } catch (error) {
    console.error('Error fetching project image set:', error);
    // Fallback to static URLs
    const context = projectType as ProjectContext;
    return {
      hero: {
        url: generateUnsplashUrl(context, 1200, 600, 'hero'),
        alt: `${projectType} hero image`
      },
      gallery: Array.from({ length: 6 }, (_, i) => ({
        url: generateUnsplashUrl(context, 400, 300, `gallery-${i}`),
        alt: `${projectType} gallery image ${i + 1}`
      })),
      thumbnails: Array.from({ length: 12 }, (_, i) => ({
        url: generateUnsplashUrl(context, 200, 150, `thumb-${i}`),
        alt: `${projectType} thumbnail ${i + 1}`
      }))
    };
  }
}

/**
 * Generate optimized image URLs with responsive sizing
 */
export function getResponsiveImageUrl(
  prompt: string,
  baseWidth: number,
  baseHeight: number,
  seed?: string
): {
  small: string;
  medium: string;
  large: string;
} {
  const context = detectProjectContext(prompt);

  return {
    small: generateUnsplashUrl(context, Math.round(baseWidth * 0.5), Math.round(baseHeight * 0.5), seed),
    medium: generateUnsplashUrl(context, baseWidth, baseHeight, seed),
    large: generateUnsplashUrl(context, Math.round(baseWidth * 1.5), Math.round(baseHeight * 1.5), seed)
  };
}

"use client";
import { useState, useEffect, useRef } from "react";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus, vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useCurrentTheme } from "@/hooks/use-current-theme";

interface Props {
  code: string;
  language: string;
  fileName: string;
  isActive: boolean;
  onComplete?: () => void;
}

export const CodeStreamWriter = ({
  code,
  language,
  fileName,
  isActive,
  onComplete
}: Props) => {
  const [displayedCode, setDisplayedCode] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const currentTheme = useCurrentTheme();

  // Reset when code changes
  useEffect(() => {
    setDisplayedCode("");
    setCurrentIndex(0);
    setIsCompleted(false);
  }, [code]);

  // Start typing animation when active
  useEffect(() => {
    if (!isActive || isCompleted || currentIndex >= code.length) {
      return;
    }

    intervalRef.current = setInterval(() => {
      setCurrentIndex(prev => {
        const next = prev + 1;
        
        if (next >= code.length) {
          setIsCompleted(true);
          onComplete?.();
          return prev;
        }
        
        return next;
      });
    }, 20); // Typing speed: 20ms per character

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive, currentIndex, code.length, isCompleted, onComplete]);

  // Update displayed code
  useEffect(() => {
    setDisplayedCode(code.slice(0, currentIndex));
  }, [currentIndex, code]);

  // Get file extension for language detection
  const getLanguageFromFileName = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    const langMap: Record<string, string> = {
      'tsx': 'tsx',
      'ts': 'typescript',
      'jsx': 'jsx',
      'js': 'javascript',
      'css': 'css',
      'html': 'html',
      'json': 'json',
      'md': 'markdown',
      'yml': 'yaml',
      'yaml': 'yaml',
    };
    return langMap[ext || ''] || 'text';
  };

  const detectedLanguage = language || getLanguageFromFileName(fileName);

  return (
    <div className="relative">
      {/* File Header */}
      <div className={`px-4 py-2 text-sm font-mono border-b ${
        currentTheme === "dark"
          ? "bg-gray-800 text-white border-gray-600"
          : "bg-gray-100 text-gray-900 border-gray-300"
      }`}>
        <div className="flex items-center gap-2">
          <div className="flex gap-1">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
          <span className={currentTheme === "dark" ? "text-gray-300" : "text-gray-600"}>
            {fileName}
          </span>
          {isActive && !isCompleted && (
            <span className="text-green-400 animate-pulse">● Writing...</span>
          )}
          {isCompleted && (
            <span className="text-green-400">✓ Complete</span>
          )}
        </div>
      </div>

      {/* Code Content */}
      <div className={`relative overflow-hidden ${
        currentTheme === "dark" ? "bg-gray-900" : "bg-white"
      }`}>
        <SyntaxHighlighter
          language={detectedLanguage}
          style={currentTheme === "dark" ? vscDarkPlus : vs}
          customStyle={{
            margin: 0,
            padding: '1rem',
            background: 'transparent',
            fontSize: '14px',
            lineHeight: '1.5',
          }}
          showLineNumbers={true}
          lineNumberStyle={{
            color: currentTheme === "dark" ? '#6b7280' : '#9ca3af',
            fontSize: '12px',
            paddingRight: '1rem',
          }}
        >
          {displayedCode}
        </SyntaxHighlighter>

        {/* Typing Cursor */}
        {isActive && !isCompleted && (
          <div
            className={`absolute w-0.5 h-5 animate-pulse ${
              currentTheme === "dark" ? "bg-white" : "bg-gray-900"
            }`}
            style={{
              top: `${Math.floor(displayedCode.split('\n').length - 1) * 24 + 16}px`,
              left: `${(displayedCode.split('\n').pop()?.length || 0) * 8.4 + 60}px`,
            }}
          />
        )}
      </div>

      {/* Progress Bar */}
      {isActive && (
        <div className={`px-4 py-2 ${
          currentTheme === "dark" ? "bg-gray-800" : "bg-gray-100"
        }`}>
          <div className={`flex items-center gap-2 text-xs ${
            currentTheme === "dark" ? "text-gray-400" : "text-gray-600"
          }`}>
            <span>Progress:</span>
            <div className={`flex-1 rounded-full h-1 ${
              currentTheme === "dark" ? "bg-gray-700" : "bg-gray-300"
            }`}>
              <div
                className="bg-green-500 h-1 rounded-full transition-all duration-100"
                style={{
                  width: `${Math.min((currentIndex / code.length) * 100, 100)}%`
                }}
              />
            </div>
            <span>{Math.round((currentIndex / code.length) * 100)}%</span>
          </div>
        </div>
      )}
    </div>
  );
};

// Enhanced progress component with code streaming
interface EnhancedProgressProps {
  projectId: string;
}

export const EnhancedGenerationProgress = ({ projectId }: EnhancedProgressProps) => {
  const [activeFileContent, setActiveFileContent] = useState<{
    fileName: string;
    content: string;
    language: string;
  } | null>(null);

  // This would be connected to your progress tracking system
  // For now, it's a placeholder for the streaming functionality
  
  return (
    <div className="space-y-4">
      {/* Your existing GenerationProgress component would go here */}
      
      {/* Code Streaming Section */}
      {activeFileContent && (
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <CodeStreamWriter
            code={activeFileContent.content}
            language={activeFileContent.language}
            fileName={activeFileContent.fileName}
            isActive={true}
            onComplete={() => {
              console.log(`Completed writing ${activeFileContent.fileName}`);
            }}
          />
        </div>
      )}
    </div>
  );
};

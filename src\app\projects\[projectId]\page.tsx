import { ProjectView } from "@/modules/projects/server/ui/views/project-view";
import { getQueryClient, trpc } from "@/trpc/server";
import { dehydrate, HydrationBoundary } from "@tanstack/react-query";
import { ErrorBoundary } from "react-error-boundary";
import { Suspense } from "react";

interface Props {
    params: Promise<{
        projectId: string;
    }>
};

const Page = async ({ params }: Props) => {
    const { projectId } = await params;
    const queryClient = getQueryClient();
    void queryClient.prefetchQuery(trpc.messages.getMany.queryOptions({
        projectId,
    }));
    void queryClient.prefetchQuery(trpc.projects.getOne.queryOptions({
        id: projectId,
    }));
    return (
    <HydrationBoundary state = {dehydrate(queryClient)}>
        <ErrorBoundary fallback={
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-xl font-semibold text-red-600 mb-2">Project Error</h1>
                    <p className="text-gray-600">Failed to load project. Please try refreshing the page.</p>
                </div>
            </div>
        }>
            <Suspense fallback={
                <div className="min-h-screen flex items-center justify-center">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-gray-600">Loading project...</p>
                    </div>
                </div>
            }>
                <ProjectView projectId={projectId} />
            </Suspense>
        </ErrorBoundary>
    </HydrationBoundary>
    );
};

export default Page;

import { Button } from "@/components/ui/button";
import { Hint } from "@/components/ui/hint";
import { Fragment } from "@/generated/prisma";
import { ExternalLinkIcon, RefreshCcwIcon, AlertTriangleIcon } from "lucide-react";
import { useState } from "react";


interface Props {
    data: Fragment;
};

export function FragmentWeb({data}: Props) {
    const [copied, setCopied] = useState(false);
    const [fragmentKey, setFragmentKey] = useState(0);
    const [iframeError, setIframeError] = useState(false);

    const onRefresh = () => {
        setFragmentKey((prev) => prev + 1);
        setIframeError(false);
    };

    const handleCopy = () => {
        navigator.clipboard.writeText(data.sandboxUrl);
        setCopied(true);
        setTimeout(() => {
            setCopied(false);
        }, 2000);
    };

    const handleIframeError = () => {
        setIframeError(true);
    };

    return (
        <div className="flex flex-col w-full h-full">
            <div className="p-2 border-b bg-sidebar flex items-center gap-x-2">
                <Hint text= "Refresh" side="bottom" align="start">
                    <Button 
                    size ="sm" 
                    variant="outline" 
                    onClick={onRefresh}                
                    >
                        <RefreshCcwIcon />
                    </Button>
                </Hint>
                <Hint text = "Click to copy" side = "bottom">
                    <Button 
                    size ="sm" 
                    variant="outline" 
                    onClick={handleCopy}
                    disabled = {!data.sandboxUrl || copied}
                    className="flex-1 justify-start text-start font-normal"
                    >
                        <span className="truncate">
                            {data.sandboxUrl}
                        </span>
                    </Button>
                </Hint>
                <Hint text= "Open in a new tab" side = "bottom" align = "start">
                    <Button
                    size = "sm"
                    disabled= {!data.sandboxUrl}
                    variant = "outline"
                    onClick={() => {
                        if (!data.sandboxUrl) return;
                        window.open(data.sandboxUrl, "_blank");
                    }}
                    >
                        <ExternalLinkIcon />
                    </Button>
                </Hint>

            </div>
            {iframeError ? (
                <div className="h-full w-full flex flex-col items-center justify-center bg-gray-50 text-gray-600">
                    <AlertTriangleIcon className="w-12 h-12 mb-4 text-orange-500" />
                    <h3 className="text-lg font-semibold mb-2">Preview Error</h3>
                    <p className="text-sm text-center mb-4 max-w-md">
                        The preview failed to load. This might be due to a build error or the sandbox is still starting up.
                    </p>
                    <Button onClick={onRefresh} variant="outline" size="sm">
                        <RefreshCcwIcon className="w-4 h-4 mr-2" />
                        Try Again
                    </Button>
                </div>
            ) : (
                <iframe
                key = {fragmentKey}
                className="h-full w-full"
                sandbox = "allow-forms allow-scripts allow-same-origin"
                loading ="lazy"
                src = {data.sandboxUrl}
                onError={handleIframeError}
                onLoad={() => setIframeError(false)}
                />
            )}
        </div>
    )
}
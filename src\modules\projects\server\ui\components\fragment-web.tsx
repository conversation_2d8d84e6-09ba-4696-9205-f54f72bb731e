import { Button } from "@/components/ui/button";
import { Hint } from "@/components/ui/hint";
import { Fragment } from "@/generated/prisma";
import { ExternalLinkIcon, RefreshCcwIcon, AlertTriangleIcon, RocketIcon, CheckIcon, XIcon, LoaderIcon } from "lucide-react";
import { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";


interface Props {
    data: Fragment;
};

export function FragmentWeb({data}: Props) {
    const [copied, setCopied] = useState(false);
    const [fragmentKey, setFragmentKey] = useState(0);
    const [iframeError, setIframeError] = useState(false);
    // Query deployment status using our new API
    const { data: deploymentStatus, refetch: refetchStatus } = useQuery({
        queryKey: ['deployment-status', data.id],
        queryFn: async () => {
            const response = await fetch(`/api/deploy/status?fragmentId=${data.id}`);
            if (!response.ok) {
                throw new Error('Failed to fetch deployment status');
            }
            return response.json();
        },
        initialData: data,
        refetchInterval: (query) => {
            const status = query.state.data?.deploymentStatus;
            return status === 'DEPLOYING' ? 3000 : false;
        },
        retry: false,
    });

    // Deploy mutation using our new API
    const deployMutation = useMutation({
        mutationFn: async () => {
            const response = await fetch('/api/deploy', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fragmentId: data.id,
                }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Deployment failed');
            }

            return response.json();
        },
        onSuccess: () => {
            toast.success('Deployment started successfully!');
            refetchStatus();
        },
        onError: (error: any) => {
            toast.error(`Deployment failed: ${error.message}`);
        },
    });

    // Redeploy is the same as deploy for now
    const redeployMutation = deployMutation;

    const onRefresh = () => {
        setFragmentKey((prev) => prev + 1);
        setIframeError(false);
    };

    const handleCopy = () => {
        navigator.clipboard.writeText(data.sandboxUrl);
        setCopied(true);
        setTimeout(() => {
            setCopied(false);
        }, 2000);
    };

    const handleIframeError = () => {
        setIframeError(true);
    };

    const handleDeploy = () => {
        deployMutation.mutate();
    };

    const handleRedeploy = () => {
        redeployMutation.mutate();
    };

    const getDeploymentStatusIcon = () => {
        const status = deploymentStatus?.deploymentStatus || data.deploymentStatus;
        switch (status) {
            case 'DEPLOYED':
                return <CheckIcon className="w-4 h-4 text-green-600" />;
            case 'DEPLOYING':
                return <LoaderIcon className="w-4 h-4 animate-spin text-blue-600" />;
            case 'FAILED':
                return <XIcon className="w-4 h-4 text-red-600" />;
            default:
                return <RocketIcon className="w-4 h-4" />;
        }
    };

    const getDeploymentStatusText = () => {
        const status = deploymentStatus?.deploymentStatus || data.deploymentStatus;
        switch (status) {
            case 'DEPLOYED':
                return 'Deployed';
            case 'DEPLOYING':
                return 'Deploying...';
            case 'FAILED':
                return 'Deploy Failed';
            default:
                return 'Deploy to Vercel';
        }
    };

    const isDeploying = (deploymentStatus?.deploymentStatus || data.deploymentStatus) === 'DEPLOYING';
    const isDeployed = (deploymentStatus?.deploymentStatus || data.deploymentStatus) === 'DEPLOYED';
    const hasFailed = (deploymentStatus?.deploymentStatus || data.deploymentStatus) === 'FAILED';
    const liveUrl = deploymentStatus?.deploymentUrl || data.deploymentUrl;

    return (
        <div className="flex flex-col w-full h-full">
            <div className="p-2 border-b bg-sidebar flex items-center gap-x-2">
                <Hint text= "Refresh" side="bottom" align="start">
                    <Button 
                    size ="sm" 
                    variant="outline" 
                    onClick={onRefresh}                
                    >
                        <RefreshCcwIcon />
                    </Button>
                </Hint>
                <Hint text = "Click to copy" side = "bottom">
                    <Button 
                    size ="sm" 
                    variant="outline" 
                    onClick={handleCopy}
                    disabled = {!data.sandboxUrl || copied}
                    className="flex-1 justify-start text-start font-normal"
                    >
                        <span className="truncate">
                            {data.sandboxUrl}
                        </span>
                    </Button>
                </Hint>
                <Hint text= "Open in a new tab" side = "bottom" align = "start">
                    <Button
                    size = "sm"
                    disabled= {!data.sandboxUrl}
                    variant = "outline"
                    onClick={() => {
                        if (!data.sandboxUrl) return;
                        window.open(data.sandboxUrl, "_blank");
                    }}
                    >
                        <ExternalLinkIcon />
                    </Button>
                </Hint>

                {/* Deploy Button */}
                <Hint text={getDeploymentStatusText()} side="bottom" align="start">
                    <Button
                        size="sm"
                        variant={isDeployed ? "default" : "outline"}
                        disabled={isDeploying || deployMutation.isPending || redeployMutation.isPending}
                        onClick={isDeployed || hasFailed ? handleRedeploy : handleDeploy}
                        className="flex items-center gap-2"
                    >
                        {getDeploymentStatusIcon()}
                        {isDeployed ? 'Redeploy' : getDeploymentStatusText()}
                    </Button>
                </Hint>

                {/* Live URL Button - only show if deployed */}
                {isDeployed && liveUrl && (
                    <Hint text="Open live site" side="bottom" align="start">
                        <Button
                            size="sm"
                            variant="default"
                            onClick={() => window.open(liveUrl, "_blank")}
                            className="bg-green-600 hover:bg-green-700"
                        >
                            <ExternalLinkIcon />
                            Live
                        </Button>
                    </Hint>
                )}

            </div>

            {/* Deployment Error Display */}
            {hasFailed && (deploymentStatus?.deploymentError || data.deploymentError) && (
                <div className="p-2 bg-red-50 border-b border-red-200 text-red-700 text-sm">
                    <div className="flex items-center gap-2">
                        <XIcon className="w-4 h-4" />
                        <span>Deployment failed: {deploymentStatus?.deploymentError || data.deploymentError}</span>
                    </div>
                </div>
            )}

            {/* Deployment Status Display */}
            {isDeployed && liveUrl && (
                <div className="p-2 bg-green-50 border-b border-green-200 text-green-700 text-sm">
                    <div className="flex items-center gap-2">
                        <CheckIcon className="w-4 h-4" />
                        <span>Successfully deployed to:</span>
                        <a
                            href={liveUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="underline hover:no-underline font-medium"
                        >
                            {liveUrl}
                        </a>
                    </div>
                </div>
            )}

            {/* Deployment Error Display */}
            {hasFailed && (deploymentStatus?.deploymentError || data.deploymentError) && (
                <div className="p-2 bg-red-50 border-b border-red-200 text-red-700 text-sm">
                    <div className="flex items-center gap-2">
                        <XIcon className="w-4 h-4" />
                        <span>Deployment failed: {deploymentStatus?.deploymentError || data.deploymentError}</span>
                    </div>
                </div>
            )}

            {iframeError ? (
                <div className="h-full w-full flex flex-col items-center justify-center bg-gray-50 text-gray-600">
                    <AlertTriangleIcon className="w-12 h-12 mb-4 text-orange-500" />
                    <h3 className="text-lg font-semibold mb-2">Preview Error</h3>
                    <p className="text-sm text-center mb-4 max-w-md">
                        The preview failed to load. This might be due to a build error or the sandbox is still starting up.
                    </p>
                    <Button onClick={onRefresh} variant="outline" size="sm">
                        <RefreshCcwIcon className="w-4 h-4 mr-2" />
                        Try Again
                    </Button>
                </div>
            ) : (
                <iframe
                key = {fragmentKey}
                className="h-full w-full"
                sandbox = "allow-forms allow-scripts allow-same-origin"
                loading ="lazy"
                src = {data.sandboxUrl}
                onError={handleIframeError}
                onLoad={() => setIframeError(false)}
                />
            )}
        </div>
    )
}
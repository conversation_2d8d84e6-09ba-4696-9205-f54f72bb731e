import { prisma } from '@/lib/db';

export type ProgressStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'ERROR';
export type ProgressType = 'SETUP' | 'DEPENDENCY' | 'FILE' | 'CONFIG' | 'OPTIMIZATION';

interface ProgressStep {
  projectId: string;
  title: string;
  description: string;
  fileName?: string;
  fileContent?: string;
  status: ProgressStatus;
  type: ProgressType;
  order: number;
  duration?: number;
}

// Track progress step
export async function updateProgressStep(
  projectId: string,
  stepId: string,
  status: ProgressStatus,
  title: string,
  description: string,
  fileContent?: string,
  type: ProgressType = 'FILE',
  order: number = 0,
  duration?: number
) {
  try {
    // Check if step already exists
    const existingStep = await prisma.generationProgress.findFirst({
      where: {
        projectId,
        OR: [
          { fileName: stepId },
          { title: title }
        ]
      },
    });

    if (existingStep) {
      // Update existing step
      return await prisma.generationProgress.update({
        where: { id: existingStep.id },
        data: {
          description,
          fileContent,
          status,
          duration,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new step
      return await prisma.generationProgress.create({
        data: {
          projectId,
          title,
          description,
          fileName: stepId,
          fileContent,
          status,
          type,
          order,
          duration,
        },
      });
    }
  } catch (error) {
    console.error('Error updating progress step:', error);
    return null;
  }
}

// Clear all progress for a project
export async function clearProjectProgress(projectId: string) {
  try {
    await prisma.generationProgress.deleteMany({
      where: { projectId },
    });
    return true;
  } catch (error) {
    console.error('Error clearing project progress:', error);
    return false;
  }
}

// Initialize project progress with common steps
export async function initializeProjectProgress(projectId: string) {
  const commonSteps = [
    {
      title: 'Setting up project structure',
      description: 'Creating project folders and basic structure',
      type: 'SETUP' as ProgressType,
      order: 1,
    },
    {
      title: 'Installing dependencies',
      description: 'Installing required packages and dependencies',
      type: 'DEPENDENCY' as ProgressType,
      order: 2,
    },
    {
      title: 'Generating configuration files',
      description: 'Creating TypeScript, Tailwind, and other config files',
      type: 'CONFIG' as ProgressType,
      order: 3,
    },
  ];

  try {
    // Clear existing progress
    await clearProjectProgress(projectId);

    // Create initial steps
    for (const step of commonSteps) {
      await prisma.generationProgress.create({
        data: {
          projectId,
          title: step.title,
          description: step.description,
          status: 'PENDING',
          type: step.type,
          order: step.order,
        },
      });
    }

    return true;
  } catch (error) {
    console.error('Error initializing project progress:', error);
    return false;
  }
}

// Add file generation step
export async function addFileGenerationStep(
  projectId: string,
  fileName: string,
  description: string,
  order: number
) {
  try {
    return await prisma.generationProgress.create({
      data: {
        projectId,
        title: `Generating ${fileName}`,
        description,
        fileName,
        status: 'PENDING',
        type: 'FILE',
        order,
      },
    });
  } catch (error) {
    console.error('Error adding file generation step:', error);
    return null;
  }
}

// Mark step as started
export async function startProgressStep(projectId: string, stepId: string) {
  return await updateProgressStep(
    projectId,
    stepId,
    'IN_PROGRESS',
    `Generating ${stepId}`,
    `Creating ${stepId}...`
  );
}

// Mark step as completed
export async function completeProgressStep(
  projectId: string,
  stepId: string,
  fileContent?: string,
  duration?: number
) {
  return await updateProgressStep(
    projectId,
    stepId,
    'COMPLETED',
    `Generated ${stepId}`,
    `Successfully created ${stepId}`,
    fileContent,
    'FILE',
    0,
    duration
  );
}

// Mark step as error
export async function errorProgressStep(
  projectId: string,
  stepId: string,
  errorMessage: string
) {
  return await updateProgressStep(
    projectId,
    stepId,
    'ERROR',
    `Error generating ${stepId}`,
    errorMessage
  );
}

// Get file generation order based on common patterns
export function getFileOrder(fileName: string): number {
  const orderMap: Record<string, number> = {
    'package.json': 10,
    'tsconfig.json': 11,
    'tailwind.config.js': 12,
    'postcss.config.js': 13,
    'vite.config.ts': 14,
    'index.html': 15,
    'src/main.tsx': 20,
    'src/App.tsx': 21,
    'src/components/Header.tsx': 30,
    'src/components/Hero.tsx': 31,
    'src/components/Services.tsx': 32,
    'src/components/Footer.tsx': 33,
    'src/styles/globals.css': 40,
    'README.md': 50,
    '.gitignore': 51,
  };

  return orderMap[fileName] || 100;
}

// Extract file name from path for display
export function getDisplayFileName(filePath: string): string {
  return filePath.split('/').pop() || filePath;
}

// Get file type description
export function getFileDescription(fileName: string): string {
  const descriptions: Record<string, string> = {
    'package.json': 'Project configuration and dependencies',
    'tsconfig.json': 'TypeScript configuration',
    'tailwind.config.js': 'Tailwind CSS configuration',
    'vite.config.ts': 'Vite build configuration',
    'App.tsx': 'Main application component',
    'Header.tsx': 'Navigation header component',
    'Hero.tsx': 'Hero section component',
    'Services.tsx': 'Services showcase component',
    'Footer.tsx': 'Footer component',
    'globals.css': 'Global styles and Tailwind imports',
    'README.md': 'Project documentation',
    '.gitignore': 'Git ignore configuration',
  };

  const baseName = getDisplayFileName(fileName);
  return descriptions[baseName] || `${baseName} component`;
}

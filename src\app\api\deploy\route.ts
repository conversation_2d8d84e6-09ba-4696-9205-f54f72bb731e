import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/db';
import { vercelService } from '@/lib/vercel-deployment';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const { fragmentId } = await request.json();
    if (!fragmentId) {
      return NextResponse.json({ error: 'Fragment ID is required' }, { status: 400 });
    }

    // Get the fragment with its files
    const fragment = await prisma.fragment.findUnique({
      where: { id: fragmentId },
      include: {
        message: {
          include: {
            project: true,
          },
        },
      },
    });

    if (!fragment) {
      return NextResponse.json({ error: 'Fragment not found' }, { status: 404 });
    }

    // Check if user owns this fragment
    if (fragment.message.project.userId !== userId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check if already deploying
    if (fragment.deploymentStatus === 'DEPLOYING') {
      return NextResponse.json({ error: 'Fragment is already being deployed' }, { status: 400 });
    }

    // Update status to deploying
    await prisma.fragment.update({
      where: { id: fragmentId },
      data: {
        deploymentStatus: 'DEPLOYING',
        deploymentError: null,
      },
    });

    try {
      console.log('Starting deployment for fragment:', fragmentId);

      // Generate project name
      const projectName = vercelService.generateProjectName(fragment.title);
      console.log('Generated project name:', projectName);

      // Log files being deployed
      const files = fragment.files as Record<string, string>;
      console.log('Files to deploy:', Object.keys(files));

      // Deploy to Vercel with timeout
      console.log('Calling Vercel deployment service...');
      const deploymentPromise = vercelService.deployProject(
        projectName,
        files,
        fragment.vercelProjectId || undefined
      );

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Deployment timeout after 60 seconds')), 60000);
      });

      const deployment = await Promise.race([deploymentPromise, timeoutPromise]) as any;
      console.log('Vercel deployment response:', deployment);

      // Get deployment URL
      const deploymentUrl = vercelService.getDeploymentUrl(deployment);
      console.log('Deployment URL:', deploymentUrl);

      // Update fragment with deployment info
      const updatedFragment = await prisma.fragment.update({
        where: { id: fragmentId },
        data: {
          deploymentStatus: deployment.state === 'READY' ? 'DEPLOYED' : 'DEPLOYING',
          deploymentUrl,
          vercelDeploymentId: deployment.id,
          deployedAt: deployment.state === 'READY' ? new Date() : null,
        },
      });

      console.log('Fragment updated successfully');

      return NextResponse.json({
        success: true,
        deploymentUrl,
        deploymentId: deployment.id,
        status: deployment.state,
        fragment: updatedFragment,
      });
    } catch (deploymentError) {
      console.error('Deployment error:', deploymentError);

      // Update fragment with error status
      const errorMessage = deploymentError instanceof Error ? deploymentError.message : 'Unknown deployment error';
      console.log('Updating fragment with error status:', errorMessage);

      await prisma.fragment.update({
        where: { id: fragmentId },
        data: {
          deploymentStatus: 'FAILED',
          deploymentError: errorMessage,
        },
      });

      return NextResponse.json({
        error: `Deployment failed: ${errorMessage}`,
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Deployment API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
    }, { status: 500 });
  }
}

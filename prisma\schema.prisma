// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Project {
  id String @id @default(uuid())
  name String
  userId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  messages Message[]
  generationProgress GenerationProgress[]

}

enum MessageRole{
  USER
  ASSISTANT
}

enum MessageType{
  RESULT
  ERROR
}

model Message{
  id String @id @default(uuid())
  content String
  role MessageRole
  type MessageType
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  projectId String
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  fragment Fragment?
}

enum DeploymentStatus {
  NOT_DEPLOYED
  DEPLOYING
  DEPLOYED
  FAILED
}

model Fragment{
  id String @id @default(uuid())
  messageId String @unique
  message Message @relation(fields: [messageId], references: [id], onDelete: Cascade)
  sandboxUrl String
  title String
  files Json

  // Deployment fields
  deploymentStatus DeploymentStatus @default(NOT_DEPLOYED)
  deploymentUrl String?
  vercelProjectId String?
  vercelDeploymentId String?
  deploymentError String?
  deployedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Usage {
  key String @id
  points Int
  expire DateTime?
}

enum ProgressStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  ERROR
}

enum ProgressType {
  SETUP
  DEPENDENCY
  FILE
  CONFIG
  OPTIMIZATION
}

model GenerationProgress {
  id String @id @default(uuid())
  projectId String
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  title String // "Generating App.tsx"
  description String // "Creating main application component"
  fileName String? // "App.tsx"
  fileContent String? // Actual generated code

  status ProgressStatus @default(PENDING)
  type ProgressType @default(FILE)
  order Int @default(0) // Display order
  duration Int? // Time taken in milliseconds

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([projectId, order])
}
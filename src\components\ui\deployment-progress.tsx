import { Progress } from "@/components/ui/progress";
import { CheckI<PERSON>, XIcon, LoaderIcon, RocketIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface DeploymentProgressProps {
  status: 'NOT_DEPLOYED' | 'DEPLOYING' | 'DEPLOYED' | 'FAILED';
  progress: number;
  message: string;
  url?: string;
  error?: string;
  className?: string;
}

export function DeploymentProgress({
  status,
  progress,
  message,
  url,
  error,
  className,
}: DeploymentProgressProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'DEPLOYED':
        return <CheckIcon className="w-4 h-4 text-green-600" />;
      case 'DEPLOYING':
        return <LoaderIcon className="w-4 h-4 animate-spin text-blue-600" />;
      case 'FAILED':
        return <XIcon className="w-4 h-4 text-red-600" />;
      default:
        return <RocketIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'DEPLOYED':
        return 'text-green-700 bg-green-50 border-green-200';
      case 'DEPLOYING':
        return 'text-blue-700 bg-blue-50 border-blue-200';
      case 'FAILED':
        return 'text-red-700 bg-red-50 border-red-200';
      default:
        return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  const getProgressColor = () => {
    switch (status) {
      case 'DEPLOYED':
        return 'bg-green-600';
      case 'DEPLOYING':
        return 'bg-blue-600';
      case 'FAILED':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  return (
    <div className={cn("p-3 border rounded-lg", getStatusColor(), className)}>
      <div className="flex items-center gap-2 mb-2">
        {getStatusIcon()}
        <span className="font-medium text-sm">{message}</span>
      </div>
      
      {status === 'DEPLOYING' && (
        <div className="space-y-2">
          <Progress 
            value={progress} 
            className="h-2"
            indicatorClassName={getProgressColor()}
          />
          <div className="text-xs opacity-75">
            {progress}% complete
          </div>
        </div>
      )}
      
      {status === 'DEPLOYED' && url && (
        <div className="mt-2">
          <a 
            href={url} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-sm underline hover:no-underline font-medium"
          >
            View live site →
          </a>
        </div>
      )}
      
      {status === 'FAILED' && error && (
        <div className="mt-2 text-xs opacity-75">
          Error: {error}
        </div>
      )}
    </div>
  );
}

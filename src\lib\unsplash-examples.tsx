/**
 * Example components showing how to use Unsplash integration
 * These examples can be referenced by the AI agent when generating projects
 */

import { 
  getContextualImageUrl, 
  getMultipleImageUrls, 
  getProjectSpecificImages,
  IMAGE_DIMENSIONS,
  type ImageDimension 
} from './image-helper';

// Example 1: Netflix-style movie card
export const NetflixMovieCard = ({ title, description }: { title: string; description: string }) => {
  const movieImages = getProjectSpecificImages.netflix.movieCards(1);
  
  return (
    <div className="relative group cursor-pointer">
      <img 
        src={movieImages[0]} 
        alt={title}
        className="w-full h-64 object-cover rounded-lg transition-transform group-hover:scale-105"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent rounded-lg" />
      <div className="absolute bottom-4 left-4 text-white">
        <h3 className="font-bold text-lg">{title}</h3>
        <p className="text-sm opacity-90">{description}</p>
      </div>
      <div className="text-xs text-gray-400 mt-2">Photo from Unsplash</div>
    </div>
  );
};

// Example 2: YouTube video thumbnail
export const YouTubeVideoThumbnail = ({ title, views, duration }: { 
  title: string; 
  views: string; 
  duration: string; 
}) => {
  const thumbnails = getProjectSpecificImages.youtube.videoThumbnails(1);
  const avatars = getProjectSpecificImages.youtube.channelAvatars(1);
  
  return (
    <div className="space-y-3">
      <div className="relative">
        <img 
          src={thumbnails[0]} 
          alt={title}
          className="w-full aspect-video object-cover rounded-lg"
        />
        <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-1 rounded">
          {duration}
        </div>
      </div>
      <div className="flex space-x-3">
        <img 
          src={avatars[0]} 
          alt="Channel avatar"
          className="w-10 h-10 rounded-full object-cover"
        />
        <div className="flex-1">
          <h3 className="font-medium text-sm line-clamp-2">{title}</h3>
          <p className="text-xs text-gray-600">{views} views</p>
        </div>
      </div>
      <div className="text-xs text-gray-400">Photos from Unsplash</div>
    </div>
  );
};

// Example 3: Spotify album cover
export const SpotifyAlbumCover = ({ album, artist }: { album: string; artist: string }) => {
  const albumCovers = getProjectSpecificImages.spotify.albumCovers(1);
  
  return (
    <div className="group cursor-pointer">
      <div className="relative">
        <img 
          src={albumCovers[0]} 
          alt={album}
          className="w-full aspect-square object-cover rounded-lg shadow-lg group-hover:shadow-xl transition-shadow"
        />
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors rounded-lg" />
      </div>
      <div className="mt-3">
        <h3 className="font-medium text-white">{album}</h3>
        <p className="text-sm text-gray-400">{artist}</p>
      </div>
      <div className="text-xs text-gray-500 mt-1">Photo from Unsplash</div>
    </div>
  );
};

// Example 4: E-commerce product card
export const ProductCard = ({ name, price, rating }: { 
  name: string; 
  price: string; 
  rating: number; 
}) => {
  const productImages = getProjectSpecificImages.store.productImages(1);
  
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <img 
        src={productImages[0]} 
        alt={name}
        className="w-full h-48 object-cover"
      />
      <div className="p-4">
        <h3 className="font-medium text-gray-900 mb-2">{name}</h3>
        <div className="flex items-center justify-between">
          <span className="text-lg font-bold text-gray-900">{price}</span>
          <div className="flex items-center">
            <span className="text-yellow-400">★</span>
            <span className="text-sm text-gray-600 ml-1">{rating}</span>
          </div>
        </div>
        <div className="text-xs text-gray-400 mt-2">Photo from Unsplash</div>
      </div>
    </div>
  );
};

// Example 5: Airbnb property listing
export const PropertyListing = ({ title, location, price, rating }: { 
  title: string; 
  location: string; 
  price: string; 
  rating: number; 
}) => {
  const propertyImages = getProjectSpecificImages.airbnb.propertyImages(1);
  
  return (
    <div className="cursor-pointer group">
      <div className="relative">
        <img 
          src={propertyImages[0]} 
          alt={title}
          className="w-full h-64 object-cover rounded-xl group-hover:brightness-95 transition-all"
        />
        <button className="absolute top-3 right-3 p-2 rounded-full bg-white/80 hover:bg-white transition-colors">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>
      </div>
      <div className="mt-3">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-gray-900">{title}</h3>
          <div className="flex items-center">
            <span className="text-sm">★ {rating}</span>
          </div>
        </div>
        <p className="text-gray-600 text-sm">{location}</p>
        <p className="text-gray-900 font-medium mt-1">{price}</p>
        <div className="text-xs text-gray-400 mt-1">Photo from Unsplash</div>
      </div>
    </div>
  );
};

// Example 6: Restaurant food item
export const FoodMenuItem = ({ name, description, price }: { 
  name: string; 
  description: string; 
  price: string; 
}) => {
  const foodImages = getProjectSpecificImages.restaurant.foodImages(1);
  
  return (
    <div className="flex space-x-4 p-4 hover:bg-gray-50 transition-colors">
      <img 
        src={foodImages[0]} 
        alt={name}
        className="w-20 h-20 object-cover rounded-lg flex-shrink-0"
      />
      <div className="flex-1">
        <div className="flex items-start justify-between">
          <div>
            <h3 className="font-medium text-gray-900">{name}</h3>
            <p className="text-sm text-gray-600 mt-1">{description}</p>
            <div className="text-xs text-gray-400 mt-1">Photo from Unsplash</div>
          </div>
          <span className="font-bold text-gray-900">{price}</span>
        </div>
      </div>
    </div>
  );
};

// Example 7: Fitness workout card
export const WorkoutCard = ({ title, duration, difficulty, calories }: { 
  title: string; 
  duration: string; 
  difficulty: string; 
  calories: number; 
}) => {
  const workoutImages = getProjectSpecificImages.fitness.workoutImages(1);
  
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden">
      <img 
        src={workoutImages[0]} 
        alt={title}
        className="w-full h-40 object-cover"
      />
      <div className="p-4">
        <h3 className="font-bold text-lg text-gray-900 mb-2">{title}</h3>
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>{duration}</span>
          <span className="capitalize">{difficulty}</span>
          <span>{calories} cal</span>
        </div>
        <div className="text-xs text-gray-400 mt-2">Photo from Unsplash</div>
      </div>
    </div>
  );
};

// Example 8: Portfolio project showcase
export const PortfolioProject = ({ title, category, description }: { 
  title: string; 
  category: string; 
  description: string; 
}) => {
  const projectImages = getProjectSpecificImages.portfolio.projectImages(1);
  
  return (
    <div className="group cursor-pointer">
      <div className="relative overflow-hidden rounded-lg">
        <img 
          src={projectImages[0]} 
          alt={title}
          className="w-full h-64 object-cover transition-transform group-hover:scale-110"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
        <div className="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity">
          <span className="text-sm font-medium">{category}</span>
        </div>
      </div>
      <div className="mt-4">
        <h3 className="font-bold text-xl text-gray-900">{title}</h3>
        <p className="text-gray-600 mt-2">{description}</p>
        <div className="text-xs text-gray-400 mt-2">Photo from Unsplash</div>
      </div>
    </div>
  );
};

// Example usage instructions for AI agent
export const USAGE_EXAMPLES = `
// Import the image helper functions
import { getProjectSpecificImages, getContextualImageUrl, getMultipleImageUrls } from '@/lib/image-helper';

// For Netflix-style projects:
const movieThumbnails = getProjectSpecificImages.netflix.movieCards(6);
const heroImage = getProjectSpecificImages.netflix.hero();

// For YouTube-style projects:
const videoThumbnails = getProjectSpecificImages.youtube.videoThumbnails(12);
const channelAvatars = getProjectSpecificImages.youtube.channelAvatars(5);

// For Spotify-style projects:
const albumCovers = getProjectSpecificImages.spotify.albumCovers(8);
const playlistCovers = getProjectSpecificImages.spotify.playlistCovers(6);

// For e-commerce projects:
const productImages = getProjectSpecificImages.store.productImages(12);
const categoryBanners = getProjectSpecificImages.store.categoryBanners(4);

// For Airbnb-style projects:
const propertyImages = getProjectSpecificImages.airbnb.propertyImages(9);
const destinationPhotos = getProjectSpecificImages.airbnb.destinations(6);

// For restaurant projects:
const foodImages = getProjectSpecificImages.restaurant.foodImages(8);
const ambiancePhotos = getProjectSpecificImages.restaurant.ambiance(4);

// For fitness projects:
const workoutImages = getProjectSpecificImages.fitness.workoutImages(6);
const equipmentPhotos = getProjectSpecificImages.fitness.equipment(8);

// For portfolio projects:
const projectImages = getProjectSpecificImages.portfolio.projectImages(6);
const profileImage = getProjectSpecificImages.portfolio.profileImage();

// For admin dashboard projects:
const teamPhotos = getProjectSpecificImages.admin.teamPhotos(8);
const officeImages = getProjectSpecificImages.admin.officeImages(4);

// For general context-aware images:
const contextualImage = getContextualImageUrl(prompt, 'card', 'unique-seed');
const multipleImages = getMultipleImageUrls(prompt, 6, 'thumbnail');
`;

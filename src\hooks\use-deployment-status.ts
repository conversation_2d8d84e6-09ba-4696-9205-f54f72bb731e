import { useTRPC } from '@/trpc/client';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

export interface DeploymentProgress {
  status: 'NOT_DEPLOYED' | 'DEPLOYING' | 'DEPLOYED' | 'FAILED';
  progress: number; // 0-100
  message: string;
  url?: string;
  error?: string;
}

export function useDeploymentStatus(fragmentId: string) {
  const trpc = useTRPC();
  const [progress, setProgress] = useState<DeploymentProgress>({
    status: 'NOT_DEPLOYED',
    progress: 0,
    message: 'Ready to deploy',
  });

  const { data: deploymentStatus, refetch } = useQuery(
    trpc.deployment.getStatus.queryOptions({
      fragmentId,
    }),
    {
      refetchInterval: (query) => {
        const status = query.state.data?.deploymentStatus;
        return status === 'DEPLOYING' ? 2000 : false; // Poll every 2 seconds if deploying
      },
    }
  );

  useEffect(() => {
    if (!deploymentStatus) return;

    const status = deploymentStatus.deploymentStatus;
    
    switch (status) {
      case 'NOT_DEPLOYED':
        setProgress({
          status: 'NOT_DEPLOYED',
          progress: 0,
          message: 'Ready to deploy',
        });
        break;
        
      case 'DEPLOYING':
        // Simulate progress for deploying state
        setProgress(prev => {
          const newProgress = Math.min(prev.progress + 10, 90);
          return {
            status: 'DEPLOYING',
            progress: newProgress,
            message: getDeployingMessage(newProgress),
          };
        });
        break;
        
      case 'DEPLOYED':
        setProgress({
          status: 'DEPLOYED',
          progress: 100,
          message: 'Successfully deployed',
          url: deploymentStatus.deploymentUrl || undefined,
        });
        break;
        
      case 'FAILED':
        setProgress({
          status: 'FAILED',
          progress: 0,
          message: 'Deployment failed',
          error: deploymentStatus.deploymentError || 'Unknown error',
        });
        break;
    }
  }, [deploymentStatus]);

  return {
    progress,
    refetch,
    isLoading: !deploymentStatus,
  };
}

function getDeployingMessage(progress: number): string {
  if (progress < 20) return 'Initializing deployment...';
  if (progress < 40) return 'Uploading files...';
  if (progress < 60) return 'Installing dependencies...';
  if (progress < 80) return 'Building application...';
  return 'Finalizing deployment...';
}

export function useDeploymentPolling(fragmentId: string, enabled: boolean = true) {
  const trpc = useTRPC();
  
  return useQuery(
    trpc.deployment.getStatus.queryOptions({
      fragmentId,
    }),
    {
      enabled,
      refetchInterval: (query) => {
        if (!enabled) return false;
        const status = query.state.data?.deploymentStatus;
        return status === 'DEPLOYING' ? 3000 : false;
      },
      refetchIntervalInBackground: true,
    }
  );
}

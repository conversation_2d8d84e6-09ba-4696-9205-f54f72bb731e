"use client";

import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangleIcon, RefreshCcwIcon, HomeIcon } from "lucide-react";
import Link from "next/link";

interface ErrorPageProps {
    error?: Error & { digest?: string };
    reset?: () => void;
}

const ErrorPage = ({ error, reset }: ErrorPageProps) => {
    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full mx-auto text-center p-6">
                <AlertTriangleIcon className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Something went wrong</h1>
                <p className="text-gray-600 mb-6">
                    {error?.message || "An unexpected error occurred. Please try again."}
                </p>
                <div className="space-y-3">
                    {reset && (
                        <Button onClick={reset} className="w-full">
                            <RefreshCcwIcon className="w-4 h-4 mr-2" />
                            Try Again
                        </Button>
                    )}
                    <Button asChild variant="outline" className="w-full">
                        <Link href="/">
                            <HomeIcon className="w-4 h-4 mr-2" />
                            Go Home
                        </Link>
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default ErrorPage;
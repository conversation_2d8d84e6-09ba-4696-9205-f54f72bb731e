/**
 * Unsplash Integration Test and Usage Examples
 * This file demonstrates the complete integration and provides testing examples
 */

import React from 'react';
import { 
  getContextualImageUrl, 
  getMultipleImageUrls, 
  getProjectSpecificImages,
  createImageArray,
  generateImageComponent,
  generateBackgroundImageStyle,
  IMAGE_DIMENSIONS
} from './image-helper';
import { UnsplashImage, UnsplashAttribution, UnsplashGallery } from './unsplash-attribution';

// Test component showing before/after comparison
export const BeforeAfterComparison = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-6">
      {/* BEFORE: Old placeholder approach */}
      <div className="space-y-4">
        <h2 className="text-xl font-bold text-gray-900">Before: Placeholder Images</h2>
        
        {/* Old Netflix card with placeholder */}
        <div className="bg-gray-200 rounded-lg p-4 space-y-3">
          <div className="w-full h-48 bg-gray-300 rounded flex items-center justify-center">
            <span className="text-gray-500">🎬 Movie Placeholder</span>
          </div>
          <h3 className="font-bold">Movie Title</h3>
          <p className="text-gray-600">Generic placeholder content</p>
        </div>

        {/* Old product card with placeholder */}
        <div className="bg-gray-200 rounded-lg p-4 space-y-3">
          <div className="w-full h-32 bg-gray-300 rounded flex items-center justify-center">
            <span className="text-gray-500">📦 Product Placeholder</span>
          </div>
          <h3 className="font-bold">Product Name</h3>
          <p className="text-gray-600">$99.99</p>
        </div>
      </div>

      {/* AFTER: Unsplash integration */}
      <div className="space-y-4">
        <h2 className="text-xl font-bold text-gray-900">After: Unsplash Images</h2>
        
        {/* New Netflix card with Unsplash */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <UnsplashImage
            src={getProjectSpecificImages.netflix.movieCards(1)[0]}
            alt="Movie poster"
            className="w-full h-48 object-cover"
            showAttribution={true}
          />
          <div className="p-4">
            <h3 className="font-bold">The Dark Knight</h3>
            <p className="text-gray-600">Contextual movie imagery from Unsplash</p>
          </div>
        </div>

        {/* New product card with Unsplash */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <UnsplashImage
            src={getProjectSpecificImages.store.productImages(1)[0]}
            alt="Product image"
            className="w-full h-32 object-cover"
            showAttribution={true}
          />
          <div className="p-4">
            <h3 className="font-bold">Premium Headphones</h3>
            <p className="text-gray-600">$99.99</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Test different project types
export const ProjectTypeTests = () => {
  const testCases = [
    {
      type: 'Netflix Clone',
      prompt: 'Build a Netflix-style movie streaming platform',
      images: getProjectSpecificImages.netflix.movieCards(3),
      context: 'movie'
    },
    {
      type: 'YouTube Clone',
      prompt: 'Create a YouTube-style video platform',
      images: getProjectSpecificImages.youtube.videoThumbnails(3),
      context: 'video'
    },
    {
      type: 'Spotify Clone',
      prompt: 'Build a Spotify-style music streaming app',
      images: getProjectSpecificImages.spotify.albumCovers(3),
      context: 'music'
    },
    {
      type: 'E-commerce Store',
      prompt: 'Create an online shopping platform',
      images: getProjectSpecificImages.store.productImages(3),
      context: 'shopping'
    },
    {
      type: 'Airbnb Clone',
      prompt: 'Build a property rental platform',
      images: getProjectSpecificImages.airbnb.propertyImages(3),
      context: 'travel'
    },
    {
      type: 'Restaurant Website',
      prompt: 'Create a restaurant website with menu',
      images: getProjectSpecificImages.restaurant.foodImages(3),
      context: 'food'
    },
    {
      type: 'Fitness App',
      prompt: 'Build a fitness tracking application',
      images: getProjectSpecificImages.fitness.workoutImages(3),
      context: 'fitness'
    },
    {
      type: 'Portfolio Website',
      prompt: 'Create a creative portfolio showcase',
      images: getProjectSpecificImages.portfolio.projectImages(3),
      context: 'creative'
    }
  ];

  return (
    <div className="space-y-8 p-6">
      <h2 className="text-2xl font-bold text-gray-900">Project Type Context Testing</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {testCases.map((testCase, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md p-4">
            <h3 className="font-bold text-lg mb-2">{testCase.type}</h3>
            <p className="text-sm text-gray-600 mb-4">{testCase.prompt}</p>
            
            <div className="grid grid-cols-3 gap-2 mb-3">
              {testCase.images.slice(0, 3).map((imageUrl, imgIndex) => (
                <UnsplashImage
                  key={imgIndex}
                  src={imageUrl}
                  alt={`${testCase.context} image ${imgIndex + 1}`}
                  className="w-full h-16 object-cover rounded"
                  showAttribution={false}
                />
              ))}
            </div>
            
            <div className="text-xs text-gray-500">
              Context: <span className="font-medium">{testCase.context}</span>
            </div>
            <UnsplashAttribution className="text-xs text-gray-400 mt-1" />
          </div>
        ))}
      </div>
    </div>
  );
};

// Test responsive image generation
export const ResponsiveImageTest = () => {
  const prompt = "Build a modern portfolio website";
  
  return (
    <div className="space-y-6 p-6">
      <h2 className="text-2xl font-bold text-gray-900">Responsive Image Testing</h2>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Different Image Sizes</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium">Thumbnail (200x150)</h4>
            <UnsplashImage
              src={getContextualImageUrl(prompt, 'thumbnail')}
              alt="Thumbnail size"
              className="w-full h-auto rounded"
            />
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">Card (400x300)</h4>
            <UnsplashImage
              src={getContextualImageUrl(prompt, 'card')}
              alt="Card size"
              className="w-full h-auto rounded"
            />
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">Hero (1200x600)</h4>
            <UnsplashImage
              src={getContextualImageUrl(prompt, 'hero')}
              alt="Hero size"
              className="w-full h-auto rounded"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Test error handling and fallbacks
export const ErrorHandlingTest = () => {
  return (
    <div className="space-y-6 p-6">
      <h2 className="text-2xl font-bold text-gray-900">Error Handling & Fallbacks</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Invalid Image URL</h3>
          <UnsplashImage
            src="https://invalid-url.com/image.jpg"
            alt="Invalid image"
            className="w-full h-48 object-cover rounded"
            fallbackClassName="w-full h-48 bg-gray-200 flex items-center justify-center text-gray-500 rounded"
          />
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Network Error Simulation</h3>
          <UnsplashImage
            src="https://httpstat.us/500"
            alt="Network error"
            className="w-full h-48 object-cover rounded"
            fallbackClassName="w-full h-48 bg-red-100 flex items-center justify-center text-red-500 rounded"
          />
        </div>
      </div>
    </div>
  );
};

// Performance test with multiple images
export const PerformanceTest = () => {
  const multipleImages = getMultipleImageUrls("Build a photo gallery app", 12, 'card');
  
  return (
    <div className="space-y-6 p-6">
      <h2 className="text-2xl font-bold text-gray-900">Performance Test - Multiple Images</h2>
      
      <UnsplashGallery
        images={multipleImages.map((url, index) => ({
          src: url,
          alt: `Gallery image ${index + 1}`,
          photographer: undefined
        }))}
        className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
        imageClassName="w-full h-32 object-cover rounded-lg"
      />
    </div>
  );
};

// Integration test component that combines everything
export const FullIntegrationTest = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold text-center text-gray-900 mb-8">
          Unsplash Integration Test Suite
        </h1>
        
        <div className="space-y-12">
          <BeforeAfterComparison />
          <ProjectTypeTests />
          <ResponsiveImageTest />
          <ErrorHandlingTest />
          <PerformanceTest />
        </div>
        
        <div className="mt-12 p-6 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-4">Integration Summary</h2>
          <div className="space-y-2 text-sm">
            <p>✅ Context-aware image selection working</p>
            <p>✅ Multiple project types supported</p>
            <p>✅ Responsive image sizing implemented</p>
            <p>✅ Error handling and fallbacks active</p>
            <p>✅ Proper Unsplash attribution included</p>
            <p>✅ Rate limiting and caching configured</p>
            <p>✅ Performance optimizations in place</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Export test utilities for development
export const testUtilities = {
  // Test context detection
  testContextDetection: (prompt: string) => {
    console.log('Testing context detection for:', prompt);
    const contextualUrl = getContextualImageUrl(prompt, 'card');
    console.log('Generated URL:', contextualUrl);
    return contextualUrl;
  },
  
  // Test multiple image generation
  testMultipleImages: (prompt: string, count: number = 5) => {
    console.log(`Testing ${count} images for:`, prompt);
    const urls = getMultipleImageUrls(prompt, count, 'card');
    console.log('Generated URLs:', urls);
    return urls;
  },
  
  // Test project-specific images
  testProjectImages: (projectType: keyof typeof getProjectSpecificImages) => {
    console.log('Testing project-specific images for:', projectType);
    const images = getProjectSpecificImages[projectType];
    if (typeof images === 'object') {
      Object.entries(images).forEach(([key, fn]) => {
        if (typeof fn === 'function') {
          console.log(`${key}:`, fn(3));
        }
      });
    }
  }
};

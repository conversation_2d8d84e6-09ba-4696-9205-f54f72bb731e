# This is a config for E2B sandbox template.
# You can use template ID (spnp6jrzuup9ivtzx83g) or template name (vibe-nextjs-test3) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("vibe-nextjs-test3") # Sync sandbox
# sandbox = await AsyncSandbox.create("vibe-nextjs-test3") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('vibe-nextjs-test3')

team_id = "542dc3f0-1bdc-4e70-98e1-ee46888bbf91"
start_cmd = "/compile_page.sh"
dockerfile = "e2b.Dockerfile"
template_name = "vibe-nextjs-test3"
template_id = "spnp6jrzuup9ivtzx83g"

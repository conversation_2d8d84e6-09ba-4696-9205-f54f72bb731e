"use client";
import Image from "next/image";
import { useTRPC } from "@/trpc/client";
import { useSuspenseQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { CodeStreamWriter } from "./code-stream-writer";
import { useCurrentTheme } from "@/hooks/use-current-theme";

interface Props {
  projectId: string;
}

const ProgressIcon = ({ status }: { status: string }) => {
  switch (status) {
    case 'PENDING':
      return <span className="text-gray-400">⏳</span>;
    case 'IN_PROGRESS':
      return <span className="text-blue-500 animate-spin">🔄</span>;
    case 'COMPLETED':
      return <span className="text-green-500">✅</span>;
    case 'ERROR':
      return <span className="text-red-500">❌</span>;
    default:
      return <span className="text-gray-400">⏳</span>;
  }
};

const ProgressBar = ({ percentage }: { percentage: number }) => {
  const currentTheme = useCurrentTheme();

  return (
    <div className={`w-full h-1 mb-2 ${
      currentTheme === "dark"
        ? "bg-gray-700"
        : "bg-gray-200"
    }`}>
      <div
        className={`h-1 transition-all duration-500 ease-out ${
          currentTheme === "dark"
            ? "bg-white"
            : "bg-gray-900"
        }`}
        style={{ width: `${percentage}%` }}
      />
    </div>
  );
};

const FileProgressItem = ({
  title,
  description,
  status,
  fileName
}: {
  title: string;
  description: string;
  status: string;
  fileName?: string;
}) => {
  const currentTheme = useCurrentTheme();

  return (
    <div className="flex items-start gap-3 py-2">
      <div className="mt-1">
        <ProgressIcon status={status} />
      </div>
      <div className="flex-1 min-w-0">
        <div className={`text-sm font-medium ${
          currentTheme === "dark" ? "text-gray-100" : "text-gray-900"
        }`}>
          {title}
        </div>
        <div className={`text-xs truncate ${
          currentTheme === "dark" ? "text-gray-400" : "text-gray-500"
        }`}>
          {description}
        </div>
        {fileName && (
          <div className={`text-xs font-mono ${
            currentTheme === "dark" ? "text-blue-400" : "text-blue-600"
          }`}>
            {fileName}
          </div>
        )}
      </div>
    </div>
  );
};

export const GenerationProgress = ({ projectId }: Props) => {
  const trpc = useTRPC();
  const currentTheme = useCurrentTheme();
  
  // Get progress statistics
  const { data: stats } = useSuspenseQuery(
    trpc.generationProgress.getProgressStats.queryOptions(
      { projectId },
      { refetchInterval: 1000 }
    )
  );

  // Get all progress steps
  const { data: progressSteps } = useSuspenseQuery(
    trpc.generationProgress.getProgress.queryOptions(
      { projectId },
      { refetchInterval: 1000 }
    )
  );

  // Get current active step
  const { data: currentStep } = useSuspenseQuery(
    trpc.generationProgress.getCurrentStep.queryOptions(
      { projectId },
      { refetchInterval: 1000 }
    )
  );

  const [displayedSteps, setDisplayedSteps] = useState<typeof progressSteps>([]);
  const [streamingFile, setStreamingFile] = useState<{
    fileName: string;
    content: string;
    language: string;
  } | null>(null);

  // Animate step appearance
  useEffect(() => {
    if (progressSteps.length > displayedSteps.length) {
      const timer = setTimeout(() => {
        setDisplayedSteps(progressSteps.slice(0, displayedSteps.length + 1));
      }, 200);
      return () => clearTimeout(timer);
    } else {
      setDisplayedSteps(progressSteps);
    }
  }, [progressSteps, displayedSteps.length]);

  // Handle code streaming for completed files
  useEffect(() => {
    if (currentStep?.status === 'COMPLETED' && currentStep.fileContent && currentStep.fileName) {
      setStreamingFile({
        fileName: currentStep.fileName,
        content: currentStep.fileContent,
        language: getLanguageFromFileName(currentStep.fileName),
      });
    }
  }, [currentStep]);

  // Get language from file extension
  const getLanguageFromFileName = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    const langMap: Record<string, string> = {
      'tsx': 'tsx',
      'ts': 'typescript',
      'jsx': 'jsx',
      'js': 'javascript',
      'css': 'css',
      'html': 'html',
      'json': 'json',
      'md': 'markdown',
    };
    return langMap[ext || ''] || 'text';
  };

  return (
    <div className={`flex flex-col p-4 rounded-lg ${
      currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50"
    }`}>
      {/* Header with Logo and Progress */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Image
            src="/logo.svg"
            alt="Vibe"
            width={16}
            height={16}
            className="shrink-0"
          />
          <span className={`text-sm font-medium ${
            currentTheme === "dark" ? "text-white" : "text-gray-900"
          }`}>Vibe</span>
        </div>
        <span className={`text-sm font-medium ${
          currentTheme === "dark" ? "text-gray-300" : "text-gray-600"
        }`}>
          {stats.percentage}%
        </span>
      </div>

      {/* Progress Bar */}
      <ProgressBar percentage={stats.percentage} />

      {/* Step Counter */}
      <div className={`text-xs mb-4 ${
        currentTheme === "dark" ? "text-gray-400" : "text-gray-500"
      }`}>
        {stats.completed} of {stats.total} steps
      </div>

      {/* Current Step Highlight */}
      {currentStep && (
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <div className={`w-2 h-2 rounded-full ${
              currentStep.status === 'IN_PROGRESS'
                ? 'bg-blue-500 animate-pulse'
                : currentStep.status === 'COMPLETED'
                ? 'bg-green-500'
                : 'bg-gray-400'
            }`} />
            <span className={`text-sm font-medium ${
              currentTheme === "dark" ? "text-white" : "text-gray-900"
            }`}>
              {currentStep.title} ({currentStep.status === 'IN_PROGRESS' ? 'In Progress' : '100%'})
            </span>
            {currentStep.fileName && (
              <span className={`text-xs px-2 py-1 rounded ${
                currentTheme === "dark"
                  ? "bg-gray-700 text-gray-300"
                  : "bg-gray-200 text-gray-600"
              }`}>
                {currentStep.fileName}
              </span>
            )}
          </div>
          <div className={`text-xs ml-4 ${
            currentTheme === "dark" ? "text-gray-400" : "text-gray-500"
          }`}>
            {currentStep.description}
          </div>
        </div>
      )}

      {/* Progress Steps List */}
      <div className="space-y-2 max-h-48 overflow-y-auto">
        {displayedSteps.map((step, index) => (
          <div
            key={step.id}
            className="flex items-center gap-3 py-1"
            style={{
              animationDelay: `${index * 100}ms`,
              opacity: 1,
              transform: 'translateY(0)',
            }}
          >
            <div className={`w-1.5 h-1.5 rounded-full flex-shrink-0 ${
              step.status === 'COMPLETED'
                ? 'bg-green-500'
                : step.status === 'IN_PROGRESS'
                ? 'bg-blue-500 animate-pulse'
                : step.status === 'ERROR'
                ? 'bg-red-500'
                : 'bg-gray-400'
            }`} />
            <span className={`text-xs flex-1 ${
              step.status === 'COMPLETED'
                ? currentTheme === "dark" ? "text-green-400" : "text-green-600"
                : step.status === 'IN_PROGRESS'
                ? currentTheme === "dark" ? "text-blue-400" : "text-blue-600"
                : step.status === 'ERROR'
                ? currentTheme === "dark" ? "text-red-400" : "text-red-600"
                : currentTheme === "dark" ? "text-gray-400" : "text-gray-500"
            }`}>
              {step.title}
            </span>
            {step.fileName && (
              <span className={`text-xs ${
                currentTheme === "dark" ? "text-gray-500" : "text-gray-400"
              }`}>
                {step.fileName}
              </span>
            )}
            {step.status === 'COMPLETED' && step.duration && (
              <span className={`text-xs ${
                currentTheme === "dark" ? "text-gray-500" : "text-gray-400"
              }`}>
                {Math.round(step.duration / 1000)}s
              </span>
            )}
          </div>
        ))}
      </div>

      {/* Credits/Summary Section */}
      <div className={`mt-4 pt-3 border-t ${
        currentTheme === "dark" ? "border-gray-700" : "border-gray-200"
      }`}>
        <div className={`text-xs ${
          currentTheme === "dark" ? "text-gray-400" : "text-gray-500"
        }`}>
        </div>
      </div>

      {/* Fallback for no progress data */}
      {stats.total === 0 && (
        <div className="flex items-center gap-2 mt-4">
          <span className="text-blue-500 animate-spin">🔄</span>
          <span className={`text-sm ${
            currentTheme === "dark" ? "text-gray-300" : "text-gray-600"
          } animate-pulse`}>
            Initializing project generation...
          </span>
        </div>
      )}

      {/* Code Streaming Section */}
      {streamingFile && (
        <div className={`mt-4 border rounded-lg overflow-hidden ${
          currentTheme === "dark" ? "border-gray-600" : "border-gray-200"
        }`}>
          <CodeStreamWriter
            code={streamingFile.content}
            language={streamingFile.language}
            fileName={streamingFile.fileName}
            isActive={true}
            onComplete={() => {
              console.log(`Completed streaming ${streamingFile.fileName}`);
              // Clear streaming file after a delay to show next file
              setTimeout(() => setStreamingFile(null), 2000);
            }}
          />
        </div>
      )}
    </div>
  );
};

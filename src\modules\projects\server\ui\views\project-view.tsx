"use client";

import { 
    Resizable<PERSON><PERSON>le,
    ResizablePanelGroup,
    ResizablePanel,
 } from "@/components/ui/resizable";
import { MessagesContainer } from "../components/messages-container";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Suspense, useState } from "react";
import { Fragment } from "@/generated/prisma";
import { ProjectHeader } from "../components/project-header";
import { FragmentWeb } from "../components/fragment-web";
import { EyeIcon, CodeIcon, CrownIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { FileExplorer } from "@/components/file-explorer";
import { UserControl } from "@/components/user-control";
import { useAuth } from "@clerk/nextjs";
import { ErrorBoundary } from "react-error-boundary";

interface Props{
    projectId: string;
};
export const ProjectView = ({projectId}: Props) =>{
    const {has} = useAuth();
    const hasProAccess = has?.({ plan: "pro" });
    const [activeFragment, setActiveFragment] = useState<Fragment | null>(null);
    const [tabState, setTabState] = useState<"preview" | "code">("preview");

return(
    <div className="h-screen">
        <ResizablePanelGroup direction="horizontal">
            <ResizablePanel 
            defaultSize={35}
            minSize={35}
            className="flex flex-col min-h-0"
            >
                <ErrorBoundary fallback={
                    <div className="p-4 text-center text-red-600">
                        <p>Failed to load project header</p>
                    </div>
                }>
                    <Suspense fallback={
                        <div className="p-4 text-center text-gray-500">
                            <p>Loading project...</p>
                        </div>
                    }>
                    <ProjectHeader projectId={projectId}/>
                    </Suspense>
                </ErrorBoundary>
                <ErrorBoundary fallback={
                    <div className="p-4 text-center text-red-600">
                        <p>Failed to load messages</p>
                    </div>
                }>
                    <Suspense fallback={
                        <div className="p-4 text-center text-gray-500">
                            <p>Loading messages...</p>
                        </div>
                    }>
                        <MessagesContainer
                        projectId={projectId}
                        activeFragment = {activeFragment}
                        setActiveFragment = {setActiveFragment}

                        />
                    </Suspense>
                </ErrorBoundary>
            </ResizablePanel>
            <ResizableHandle className="hover:bg-primary transition-colors"/>
            <ResizablePanel
            defaultSize={65}
            minSize={50}
            >
                <Tabs
                className="h-full gap-y-0"
                defaultValue = "preview"
                value = {tabState}
                onValueChange={(value) => setTabState(value as "preview" | "code")}
                >
                    <div className="w-full flex items-center p-2 border-b gap-x-2">
                        <TabsList className="h-8 p-0 border rounded-md">
                            <TabsTrigger value="preview" className="rounded-md">
                                <EyeIcon /> <span>Demo</span>
                            </TabsTrigger>
                            <TabsTrigger value="code" className="rounded-md">
                                <CodeIcon /> <span>Code</span>
                            </TabsTrigger>
                        </TabsList>
                        <div className="ml-auto flex items-center gap-x-2">
                            {!hasProAccess && (
                                <Button asChild size="sm" variant = "default">
                                    <Link href="/pricing">
                                        <CrownIcon /> Upgrade
                                    </Link>
                                </Button>
                            )}
                            <UserControl />
                        </div>
                    </div>
                    <TabsContent value="preview">
                        {!!activeFragment && <FragmentWeb data = {activeFragment}/>}
                    </TabsContent>
                    <TabsContent value="code" className="min-h-0">
                        {!!activeFragment?.files && (
                            <FileExplorer
                            files = {activeFragment.files as {[path: string]: string}}
                            />
                        )}
                    </TabsContent>
                </Tabs>
            </ResizablePanel>
        </ResizablePanelGroup>
    </div>
)
};
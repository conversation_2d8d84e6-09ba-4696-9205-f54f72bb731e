import { VercelFile } from './vercel-deployment';

export interface BuildPipelineOptions {
  projectName: string;
  files: Record<string, string>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

export class VercelBuildPipeline {
  
  /**
   * Prepare files for Vercel deployment - AI generates all necessary files with fallbacks
   * This pipeline processes AI-generated files and adds essential files if missing
   */
  static prepareProjectFiles(options: BuildPipelineOptions): VercelFile[] {
    const { projectName, files, dependencies = {}, devDependencies = {} } = options;
    const vercelFiles: VercelFile[] = [];

    // Process all AI-generated files first (with syntax fixes)
    Object.entries(files).forEach(([path, content]) => {
      // Fix common syntax errors in AI-generated code
      const fixedContent = this.fixCommonSyntaxErrors(content, path);
      vercelFiles.push({
        file: path,
        data: Buffer.from(fixedContent, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    });

    // Add essential files if AI didn't generate them (fallback mechanism)
    const existingFiles = new Set(Object.keys(files));
    console.log('Existing files from AI:', Array.from(existingFiles));

    // 1. Ensure package.json exists (with dependency detection)
    if (!existingFiles.has('package.json')) {
      console.log('AI did not generate package.json, creating fallback');
      const packageJson = this.generateFallbackPackageJson(projectName, dependencies, devDependencies, files);
      vercelFiles.push({
        file: 'package.json',
        data: Buffer.from(JSON.stringify(packageJson, null, 2), 'utf8').toString('base64'),
        encoding: 'base64'
      });
    } else {
      // Check if existing package.json has all required dependencies
      const existingPackageJson = JSON.parse(files['package.json']);
      const missingDeps = this.detectMissingDependencies(files, existingPackageJson);
      if (missingDeps.length > 0) {
        console.log('Adding missing dependencies to package.json:', missingDeps);
        const updatedPackageJson = this.addMissingDependencies(existingPackageJson, missingDeps);
        // Update the vercelFiles array
        const packageIndex = vercelFiles.findIndex(f => f.file === 'package.json');
        if (packageIndex >= 0) {
          vercelFiles[packageIndex].data = Buffer.from(JSON.stringify(updatedPackageJson, null, 2), 'utf8').toString('base64');
        } else {
          vercelFiles.push({
            file: 'package.json',
            data: Buffer.from(JSON.stringify(updatedPackageJson, null, 2), 'utf8').toString('base64'),
            encoding: 'base64'
          });
        }
      }
    }

    // 2. Ensure .npmrc exists for dependency resolution
    if (!existingFiles.has('.npmrc')) {
      console.log('AI did not generate .npmrc, creating fallback');
      const npmrc = this.generateFallbackNpmrc();
      vercelFiles.push({
        file: '.npmrc',
        data: Buffer.from(npmrc, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 3. Ensure app/layout.tsx exists (CRITICAL for Next.js App Router)
    console.log('Checking for app/layout.tsx:', existingFiles.has('app/layout.tsx'));
    if (!existingFiles.has('app/layout.tsx')) {
      console.log('AI did not generate app/layout.tsx, creating fallback');
      const layout = this.generateFallbackLayout(this.needsTailwindFromFiles(files));
      vercelFiles.push({
        file: 'app/layout.tsx',
        data: Buffer.from(layout, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    } else {
      console.log('AI generated app/layout.tsx, using AI version');
    }

    // 4. Ensure next.config.js exists
    if (!existingFiles.has('next.config.js')) {
      console.log('AI did not generate next.config.js, creating fallback');
      const nextConfig = this.generateFallbackNextConfig();
      vercelFiles.push({
        file: 'next.config.js',
        data: Buffer.from(nextConfig, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 5. Ensure tailwind.config.js exists if Tailwind is used
    if (this.needsTailwindFromFiles(files) && !existingFiles.has('tailwind.config.js')) {
      console.log('AI did not generate tailwind.config.js, creating fallback');
      const tailwindConfig = this.generateFallbackTailwindConfig();
      vercelFiles.push({
        file: 'tailwind.config.js',
        data: Buffer.from(tailwindConfig, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 6. Ensure app/globals.css exists if Tailwind is used
    if (this.needsTailwindFromFiles(files) && !existingFiles.has('app/globals.css')) {
      console.log('AI did not generate app/globals.css, creating fallback');
      const globalsCss = this.generateFallbackGlobalsCss();
      vercelFiles.push({
        file: 'app/globals.css',
        data: Buffer.from(globalsCss, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 7. Generate missing UI components
    const missingComponents = this.detectMissingUIComponents(files);
    console.log('Missing UI components detected:', missingComponents);
    missingComponents.forEach(component => {
      console.log(`AI did not generate components/ui/${component}.tsx, creating fallback`);
      const componentCode = this.generateUIComponent(component);
      vercelFiles.push({
        file: `components/ui/${component}.tsx`,
        data: Buffer.from(componentCode, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    });

    return vercelFiles;
  }

  /**
   * Generate fallback package.json if AI didn't create one
   */
  private static generateFallbackPackageJson(
    projectName: string,
    userDependencies: Record<string, string>,
    userDevDependencies: Record<string, string>,
    files: Record<string, string>
  ) {
    // Detect if Tailwind is needed
    const needsTailwind = this.needsTailwindFromFiles(files);

    const baseDependencies = {
      'next': '15.3.4',
      'react': '18.2.0',
      'react-dom': '18.2.0',
      ...userDependencies
    };

    const baseDevDependencies: Record<string, string> = {
      '@types/node': '^20',
      '@types/react': '^18',
      '@types/react-dom': '^18',
      'typescript': '^5',
      ...userDevDependencies
    };

    // Add Tailwind if needed
    if (needsTailwind) {
      baseDevDependencies['tailwindcss'] = '^3.4.0';
      baseDevDependencies['autoprefixer'] = '^10.4.0';
      baseDevDependencies['postcss'] = '^8.4.0';
    }

    return {
      name: projectName,
      version: '0.1.0',
      private: true,
      scripts: {
        dev: 'next dev',
        build: 'next build',
        start: 'next start',
        lint: 'next lint'
      },
      dependencies: baseDependencies,
      devDependencies: baseDevDependencies,
      overrides: {
        "react": "18.2.0",
        "react-dom": "18.2.0"
      }
    };
  }

  /**
   * Generate fallback .npmrc if AI didn't create one
   */
  private static generateFallbackNpmrc(): string {
    return `legacy-peer-deps=true
auto-install-peers=true
strict-peer-deps=false
fund=false
audit=false`;
  }

  /**
   * Generate fallback app/layout.tsx if AI didn't create one
   */
  private static generateFallbackLayout(needsTailwind: boolean): string {
    const tailwindImport = needsTailwind ? `import './globals.css'` : '';

    return `import type { Metadata } from 'next'
${tailwindImport}

export const metadata: Metadata = {
  title: 'Generated App',
  description: 'Generated by AI',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}`;
  }

  /**
   * Generate fallback next.config.js if AI didn't create one
   */
  private static generateFallbackNextConfig(): string {
    return `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['images.unsplash.com'],
  },
}

module.exports = nextConfig`;
  }

  /**
   * Generate fallback tailwind.config.js if AI didn't create one
   */
  private static generateFallbackTailwindConfig(): string {
    return `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`;
  }

  /**
   * Generate fallback app/globals.css if AI didn't create one
   */
  private static generateFallbackGlobalsCss(): string {
    return `@tailwind base;
@tailwind components;
@tailwind utilities;`;
  }

  /**
   * Check if Tailwind CSS is needed based on file contents
   */
  private static needsTailwindFromFiles(files: Record<string, string>): boolean {
    const tailwindPattern = /class(Name)?=["'][^"']*\b(bg-|text-|p-|m-|w-|h-|flex|grid|border|rounded)/;
    return Object.values(files).some(content => tailwindPattern.test(content));
  }

  /**
   * Fix common syntax errors in AI-generated code
   */
  private static fixCommonSyntaxErrors(content: string, filePath: string): string {
    let fixedContent = content;

    // Fix "use client" directive syntax errors
    if (filePath.endsWith('.tsx') || filePath.endsWith('.ts')) {
      // Fix missing quotes around "use client"
      fixedContent = fixedContent.replace(/^use client\s*$/m, '"use client";');
      fixedContent = fixedContent.replace(/^use client\s*;$/m, '"use client";');

      // Fix missing semicolon after "use client"
      fixedContent = fixedContent.replace(/^"use client"$/m, '"use client";');

      // Fix single quotes to double quotes for consistency
      fixedContent = fixedContent.replace(/^'use client'\s*;?$/m, '"use client";');
    }

    return fixedContent;
  }

  /**
   * Detect missing UI components from import statements
   */
  private static detectMissingUIComponents(files: Record<string, string>): string[] {
    const allContent = Object.values(files).join('\n');
    const missingComponents: string[] = [];

    // Look for imports from @/components/ui/
    const importPattern = /@\/components\/ui\/([a-zA-Z-]+)/g;
    const matches = allContent.matchAll(importPattern);

    const componentNames = new Set<string>();
    for (const match of matches) {
      componentNames.add(match[1]);
    }

    // Check which components are missing
    const existingFiles = new Set(Object.keys(files));
    for (const componentName of componentNames) {
      if (!existingFiles.has(`components/ui/${componentName}.tsx`)) {
        missingComponents.push(componentName);
      }
    }

    return missingComponents;
  }

  /**
   * Generate UI component code
   */
  private static generateUIComponent(componentName: string): string {
    switch (componentName) {
      case 'button':
        return this.generateButtonComponent();
      case 'input':
        return this.generateInputComponent();
      case 'checkbox':
        return this.generateCheckboxComponent();
      case 'card':
        return this.generateCardComponent();
      case 'textarea':
        return this.generateTextareaComponent();
      case 'select':
        return this.generateSelectComponent();
      case 'dialog':
        return this.generateDialogComponent();
      default:
        return this.generateGenericComponent(componentName);
    }
  }

  /**
   * Generate Button component
   */
  private static generateButtonComponent(): string {
    return `import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline' | 'secondary' | 'destructive' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  asChild?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
  asChild = false,
  ...props
}) => {
  const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';

  const variants = {
    default: 'bg-blue-600 text-white hover:bg-blue-700',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
    destructive: 'bg-red-600 text-white hover:bg-red-700',
    ghost: 'hover:bg-gray-100 text-gray-900',
    link: 'text-blue-600 underline-offset-4 hover:underline'
  };

  const sizes = {
    sm: 'text-sm px-3 py-1.5 h-8',
    md: 'text-sm px-4 py-2 h-10',
    lg: 'text-base px-6 py-3 h-12',
    icon: 'h-10 w-10'
  };

  if (asChild) {
    return React.cloneElement(children as React.ReactElement, {
      className: \`\${baseStyles} \${variants[variant]} \${sizes[size]} \${className}\`
    });
  }

  return (
    <button
      className={\`\${baseStyles} \${variants[variant]} \${sizes[size]} \${className}\`}
      {...props}
    >
      {children}
    </button>
  );
};`;
  }

  /**
   * Generate Input component
   */
  private static generateInputComponent(): string {
    return `import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

export const Input: React.FC<InputProps> = ({ className = '', ...props }) => {
  return (
    <input
      className={\`flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \${className}\`}
      {...props}
    />
  );
};`;
  }

  /**
   * Generate Checkbox component (simple, no external dependencies)
   */
  private static generateCheckboxComponent(): string {
    return `import React from 'react';

interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  label?: string;
  onCheckedChange?: (checked: boolean) => void;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  label,
  className = '',
  id,
  onCheckedChange,
  onChange,
  ...props
}) => {
  const checkboxId = id || \`checkbox-\${Math.random().toString(36).substr(2, 9)}\`;

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    // Support both onChange and onCheckedChange patterns
    onChange?.(event);
    onCheckedChange?.(event.target.checked);
  };

  return (
    <div className="flex items-center space-x-2">
      <input
        type="checkbox"
        id={checkboxId}
        className={\`h-4 w-4 rounded border border-gray-300 text-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \${className}\`}
        onChange={handleChange}
        {...props}
      />
      {label && (
        <label
          htmlFor={checkboxId}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {label}
        </label>
      )}
    </div>
  );
};`;
  }

  /**
   * Generate Card component
   */
  private static generateCardComponent(): string {
    return `import React from 'react';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {}

export const Card: React.FC<CardProps> = ({ className = '', children, ...props }) => {
  return (
    <div
      className={\`rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm \${className}\`}
      {...props}
    >
      {children}
    </div>
  );
};

export const CardHeader: React.FC<CardProps> = ({ className = '', children, ...props }) => {
  return (
    <div className={\`flex flex-col space-y-1.5 p-6 \${className}\`} {...props}>
      {children}
    </div>
  );
};

export const CardContent: React.FC<CardProps> = ({ className = '', children, ...props }) => {
  return (
    <div className={\`p-6 pt-0 \${className}\`} {...props}>
      {children}
    </div>
  );
};`;
  }

  /**
   * Generate Textarea component
   */
  private static generateTextareaComponent(): string {
    return `import React from 'react';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

export const Textarea: React.FC<TextareaProps> = ({ className = '', ...props }) => {
  return (
    <textarea
      className={\`flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \${className}\`}
      {...props}
    />
  );
};`;
  }

  /**
   * Generate Select component
   */
  private static generateSelectComponent(): string {
    return `import React from 'react';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {}

export const Select: React.FC<SelectProps> = ({ className = '', children, ...props }) => {
  return (
    <select
      className={\`flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \${className}\`}
      {...props}
    >
      {children}
    </select>
  );
};`;
  }

  /**
   * Generate Dialog component
   */
  private static generateDialogComponent(): string {
    return `import React from 'react';

interface DialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children: React.ReactNode;
}

export const Dialog: React.FC<DialogProps> = ({ open, onOpenChange, children }) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={() => onOpenChange?.(false)}
      />
      <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
        {children}
      </div>
    </div>
  );
};

export const DialogContent: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = ''
}) => {
  return (
    <div className={\`p-6 \${className}\`}>
      {children}
    </div>
  );
};`;
  }

  /**
   * Generate generic component
   */
  private static generateGenericComponent(componentName: string): string {
    const capitalizedName = componentName.charAt(0).toUpperCase() + componentName.slice(1);
    return `import React from 'react';

interface ${capitalizedName}Props extends React.HTMLAttributes<HTMLDivElement> {}

export const ${capitalizedName}: React.FC<${capitalizedName}Props> = ({ className = '', children, ...props }) => {
  return (
    <div className={\`\${className}\`} {...props}>
      {children}
    </div>
  );
};`;
  }

  /**
   * Detect missing dependencies from import statements
   */
  private static detectMissingDependencies(files: Record<string, string>, packageJson: any): string[] {
    const allContent = Object.values(files).join('\n');
    const missingDeps: string[] = [];

    // Common dependency patterns
    const dependencyPatterns = [
      { pattern: /@radix-ui\/react-checkbox/, dep: '@radix-ui/react-checkbox' },
      { pattern: /@radix-ui\/react-slot/, dep: '@radix-ui/react-slot' },
      { pattern: /@radix-ui\/react-dialog/, dep: '@radix-ui/react-dialog' },
      { pattern: /@radix-ui\/react-select/, dep: '@radix-ui/react-select' },
      { pattern: /class-variance-authority/, dep: 'class-variance-authority' },
      { pattern: /clsx/, dep: 'clsx' },
      { pattern: /tailwind-merge/, dep: 'tailwind-merge' },
      { pattern: /lucide-react/, dep: 'lucide-react' },
      { pattern: /@heroicons\/react/, dep: '@heroicons/react' },
      { pattern: /framer-motion/, dep: 'framer-motion' },
    ];

    const existingDeps = {
      ...packageJson.dependencies || {},
      ...packageJson.devDependencies || {}
    };

    dependencyPatterns.forEach(({ pattern, dep }) => {
      if (pattern.test(allContent) && !existingDeps[dep]) {
        missingDeps.push(dep);
      }
    });

    return missingDeps;
  }

  /**
   * Add missing dependencies to package.json
   */
  private static addMissingDependencies(packageJson: any, missingDeps: string[]): any {
    const updatedPackageJson = { ...packageJson };

    // Ensure dependencies object exists
    if (!updatedPackageJson.dependencies) {
      updatedPackageJson.dependencies = {};
    }

    // Add missing dependencies with appropriate versions
    const dependencyVersions: Record<string, string> = {
      '@radix-ui/react-checkbox': '^1.0.4',
      '@radix-ui/react-slot': '^1.0.2',
      '@radix-ui/react-dialog': '^1.0.5',
      '@radix-ui/react-select': '^1.2.2',
      'class-variance-authority': '^0.7.0',
      'clsx': '^2.0.0',
      'tailwind-merge': '^2.0.0',
      'lucide-react': '^0.268.0',
      '@heroicons/react': '^2.0.18',
      'framer-motion': '^10.16.4',
    };

    missingDeps.forEach(dep => {
      updatedPackageJson.dependencies[dep] = dependencyVersions[dep] || '^1.0.0';
    });

    return updatedPackageJson;
  }
}

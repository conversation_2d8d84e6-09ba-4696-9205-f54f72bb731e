import { VercelFile } from './vercel-deployment';

export interface BuildPipelineOptions {
  projectName: string;
  files: Record<string, string>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

export class VercelBuildPipeline {
  
  /**
   * Prepare a complete Next.js project structure for Vercel deployment
   */
  static prepareProjectFiles(options: BuildPipelineOptions): VercelFile[] {
    const { projectName, files, dependencies = {}, devDependencies = {} } = options;
    const vercelFiles: VercelFile[] = [];

    // Check if Tailwind is needed
    const needsTailwind = this.needsTailwindFromFiles(files) || 'tailwindcss' in devDependencies;

    // 1. Process user-generated files
    Object.entries(files).forEach(([path, content]) => {
      vercelFiles.push({
        file: path,
        data: Buffer.from(content, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    });

    // 2. Generate package.json with comprehensive dependencies
    const packageJson = this.generatePackageJson(projectName, dependencies, devDependencies, needsTailwind);
    vercelFiles.push({
      file: 'package.json',
      data: Buffer.from(JSON.stringify(packageJson, null, 2), 'utf8').toString('base64'),
      encoding: 'base64'
    });

    // 3. Generate Next.js configuration
    const nextConfig = this.generateNextConfig();
    vercelFiles.push({
      file: 'next.config.js',
      data: Buffer.from(nextConfig, 'utf8').toString('base64'),
      encoding: 'base64'
    });

    // 4. Generate TypeScript configuration
    const tsConfig = this.generateTsConfig();
    vercelFiles.push({
      file: 'tsconfig.json',
      data: Buffer.from(JSON.stringify(tsConfig, null, 2), 'utf8').toString('base64'),
      encoding: 'base64'
    });

    // 5. Generate Tailwind CSS configuration (if needed)
    if (needsTailwind) {
      const tailwindConfig = this.generateTailwindConfig();
      vercelFiles.push({
        file: 'tailwind.config.js',
        data: Buffer.from(tailwindConfig, 'utf8').toString('base64'),
        encoding: 'base64'
      });

      const tailwindCss = this.generateTailwindCss();
      vercelFiles.push({
        file: 'app/globals.css',
        data: Buffer.from(tailwindCss, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 6. Generate missing UI components
    const missingComponents = this.detectMissingComponents(files);
    missingComponents.forEach(component => {
      const componentCode = this.generateUIComponent(component);
      vercelFiles.push({
        file: `components/ui/${component}.tsx`,
        data: Buffer.from(componentCode, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    });

    // 7. Generate layout.tsx if not present
    if (!files['app/layout.tsx']) {
      const layout = this.generateLayout(needsTailwind);
      vercelFiles.push({
        file: 'app/layout.tsx',
        data: Buffer.from(layout, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 8. Generate .npmrc for dependency resolution
    const npmrc = this.generateNpmrc();
    vercelFiles.push({
      file: '.npmrc',
      data: Buffer.from(npmrc, 'utf8').toString('base64'),
      encoding: 'base64'
    });

    // 9. Generate .gitignore
    const gitignore = this.generateGitignore();
    vercelFiles.push({
      file: '.gitignore',
      data: Buffer.from(gitignore, 'utf8').toString('base64'),
      encoding: 'base64'
    });

    return vercelFiles;
  }

  private static generatePackageJson(
    projectName: string,
    userDependencies: Record<string, string>,
    userDevDependencies: Record<string, string>,
    needsTailwind: boolean
  ) {
    const baseDependencies = {
      'next': '15.3.4',
      'react': '18.2.0',        // Exact version for guaranteed compatibility
      'react-dom': '18.2.0',    // Exact version for guaranteed compatibility
      ...userDependencies
    };

    const baseDevDependencies = {
      '@types/node': '^20',
      '@types/react': '^18',
      '@types/react-dom': '^18',
      'typescript': '^5',
      ...userDevDependencies
    };

    // Add Tailwind if needed
    if (needsTailwind) {
      baseDevDependencies['tailwindcss'] = '^3.4.0';
      baseDevDependencies['autoprefixer'] = '^10.4.0';
      baseDevDependencies['postcss'] = '^8.4.0';
    }

    return {
      name: projectName,
      version: '0.1.0',
      private: true,
      scripts: {
        dev: 'next dev',
        build: 'next build',
        start: 'next start',
        lint: 'next lint'
      },
      dependencies: baseDependencies,
      devDependencies: baseDevDependencies,
      overrides: {
        "react": "18.2.0",
        "react-dom": "18.2.0"
      }
    };
  }

  private static generateNextConfig(): string {
    return `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['images.unsplash.com'],
  },
}

module.exports = nextConfig`;
  }

  private static generateTsConfig() {
    return {
      compilerOptions: {
        target: 'es5',
        lib: ['dom', 'dom.iterable', 'es6'],
        allowJs: true,
        skipLibCheck: true,
        strict: true,
        noEmit: true,
        esModuleInterop: true,
        module: 'esnext',
        moduleResolution: 'bundler',
        resolveJsonModule: true,
        isolatedModules: true,
        jsx: 'preserve',
        incremental: true,
        plugins: [
          {
            name: 'next'
          }
        ],
        baseUrl: '.',
        paths: {
          '@/*': ['./*']
        }
      },
      include: ['next-env.d.ts', '**/*.ts', '**/*.tsx', '.next/types/**/*.ts'],
      exclude: ['node_modules']
    };
  }

  private static needsTailwindFromFiles(files: Record<string, string>): boolean {
    // Check if any file contains Tailwind classes
    const tailwindPattern = /class(Name)?=["'][^"']*\b(bg-|text-|p-|m-|w-|h-|flex|grid|border|rounded)/;
    return Object.values(files).some(content => tailwindPattern.test(content));
  }

  private static detectMissingComponents(files: Record<string, string>): string[] {
    const allContent = Object.values(files).join('\n');
    const missingComponents: string[] = [];

    // Common UI components that might be imported
    const componentPatterns = [
      { name: 'button', pattern: /@\/components\/ui\/button/ },
      { name: 'input', pattern: /@\/components\/ui\/input/ },
      { name: 'checkbox', pattern: /@\/components\/ui\/checkbox/ },
      { name: 'card', pattern: /@\/components\/ui\/card/ },
      { name: 'dialog', pattern: /@\/components\/ui\/dialog/ },
      { name: 'select', pattern: /@\/components\/ui\/select/ },
      { name: 'textarea', pattern: /@\/components\/ui\/textarea/ },
    ];

    componentPatterns.forEach(({ name, pattern }) => {
      if (pattern.test(allContent)) {
        missingComponents.push(name);
      }
    });

    return missingComponents;
  }

  private static generateUIComponent(componentName: string): string {
    switch (componentName) {
      case 'button':
        return this.generateButtonComponent();
      case 'input':
        return this.generateInputComponent();
      case 'checkbox':
        return this.generateCheckboxComponent();
      case 'card':
        return this.generateCardComponent();
      case 'dialog':
        return this.generateDialogComponent();
      case 'select':
        return this.generateSelectComponent();
      case 'textarea':
        return this.generateTextareaComponent();
      default:
        return this.generateGenericComponent(componentName);
    }
  }

  private static generateTailwindConfig(): string {
    return `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`;
  }

  private static generateTailwindCss(): string {
    return `@tailwind base;
@tailwind components;
@tailwind utilities;`;
  }

  private static generateLayout(includeTailwind: boolean): string {
    const cssImport = includeTailwind ? "import './globals.css'" : '';
    
    return `import type { Metadata } from 'next'
${cssImport}

export const metadata: Metadata = {
  title: 'Generated App',
  description: 'Generated by AI',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}`;
  }

  private static generateNpmrc(): string {
    return `legacy-peer-deps=true
auto-install-peers=true
strict-peer-deps=false
fund=false
audit=false`;
  }

  private static generateGitignore(): string {
    return `# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts`;
  }

  private static generateButtonComponent(): string {
    return `import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline' | 'secondary' | 'destructive' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  asChild?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
  asChild = false,
  ...props
}) => {
  const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';

  const variants = {
    default: 'bg-blue-600 text-white hover:bg-blue-700',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
    destructive: 'bg-red-600 text-white hover:bg-red-700',
    ghost: 'hover:bg-gray-100 text-gray-900',
    link: 'text-blue-600 underline-offset-4 hover:underline'
  };

  const sizes = {
    sm: 'text-sm px-3 py-1.5 h-8',
    md: 'text-sm px-4 py-2 h-10',
    lg: 'text-base px-6 py-3 h-12',
    icon: 'h-10 w-10'
  };

  if (asChild) {
    return React.cloneElement(children as React.ReactElement, {
      className: \`\${baseStyles} \${variants[variant]} \${sizes[size]} \${className}\`
    });
  }

  return (
    <button
      className={\`\${baseStyles} \${variants[variant]} \${sizes[size]} \${className}\`}
      {...props}
    >
      {children}
    </button>
  );
};`;
  }

  private static generateInputComponent(): string {
    return `import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

export const Input: React.FC<InputProps> = ({ className = '', ...props }) => {
  return (
    <input
      className={\`flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \${className}\`}
      {...props}
    />
  );
};`;
  }

  private static generateCheckboxComponent(): string {
    return `import React from 'react';

interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  label?: string;
  onCheckedChange?: (checked: boolean) => void;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  label,
  className = '',
  id,
  onCheckedChange,
  onChange,
  ...props
}) => {
  const checkboxId = id || \`checkbox-\${Math.random().toString(36).substr(2, 9)}\`;

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    // Support both onChange and onCheckedChange patterns
    onChange?.(event);
    onCheckedChange?.(event.target.checked);
  };

  return (
    <div className="flex items-center space-x-2">
      <input
        type="checkbox"
        id={checkboxId}
        className={\`peer h-4 w-4 shrink-0 rounded-sm border border-gray-300 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white \${className}\`}
        onChange={handleChange}
        {...props}
      />
      {label && (
        <label
          htmlFor={checkboxId}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {label}
        </label>
      )}
    </div>
  );
};`;
  }

  private static generateCardComponent(): string {
    return `import React from 'react';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {}

export const Card: React.FC<CardProps> = ({ className = '', children, ...props }) => {
  return (
    <div
      className={\`rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm \${className}\`}
      {...props}
    >
      {children}
    </div>
  );
};

export const CardHeader: React.FC<CardProps> = ({ className = '', children, ...props }) => {
  return (
    <div className={\`flex flex-col space-y-1.5 p-6 \${className}\`} {...props}>
      {children}
    </div>
  );
};

export const CardContent: React.FC<CardProps> = ({ className = '', children, ...props }) => {
  return (
    <div className={\`p-6 pt-0 \${className}\`} {...props}>
      {children}
    </div>
  );
};`;
  }

  private static generateTextareaComponent(): string {
    return `import React from 'react';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

export const Textarea: React.FC<TextareaProps> = ({ className = '', ...props }) => {
  return (
    <textarea
      className={\`flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \${className}\`}
      {...props}
    />
  );
};`;
  }

  private static generateSelectComponent(): string {
    return `import React from 'react';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {}

export const Select: React.FC<SelectProps> = ({ className = '', children, ...props }) => {
  return (
    <select
      className={\`flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \${className}\`}
      {...props}
    >
      {children}
    </select>
  );
};`;
  }

  private static generateDialogComponent(): string {
    return `import React from 'react';

interface DialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children: React.ReactNode;
}

export const Dialog: React.FC<DialogProps> = ({ open, onOpenChange, children }) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={() => onOpenChange?.(false)}
      />
      <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
        {children}
      </div>
    </div>
  );
};

export const DialogContent: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = ''
}) => {
  return (
    <div className={\`p-6 \${className}\`}>
      {children}
    </div>
  );
};`;
  }

  private static generateGenericComponent(componentName: string): string {
    const capitalizedName = componentName.charAt(0).toUpperCase() + componentName.slice(1);
    return `import React from 'react';

interface ${capitalizedName}Props extends React.HTMLAttributes<HTMLDivElement> {}

export const ${capitalizedName}: React.FC<${capitalizedName}Props> = ({ className = '', children, ...props }) => {
  return (
    <div className={\`\${className}\`} {...props}>
      {children}
    </div>
  );
};`;
  }
}

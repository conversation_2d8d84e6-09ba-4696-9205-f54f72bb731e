import { VercelFile } from './vercel-deployment';

export interface BuildPipelineOptions {
  projectName: string;
  files: Record<string, string>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

export class VercelBuildPipeline {
  
  /**
   * Prepare a complete Next.js project structure for Vercel deployment
   */
  static prepareProjectFiles(options: BuildPipelineOptions): VercelFile[] {
    const { projectName, files, dependencies = {}, devDependencies = {} } = options;
    const vercelFiles: VercelFile[] = [];

    // Check if Tailwind is needed
    const needsTailwind = this.needsTailwindFromFiles(files) || 'tailwindcss' in devDependencies;

    // 1. Process user-generated files
    Object.entries(files).forEach(([path, content]) => {
      vercelFiles.push({
        file: path,
        data: Buffer.from(content, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    });

    // 2. Generate package.json with comprehensive dependencies
    const packageJson = this.generatePackageJson(projectName, dependencies, devDependencies, needsTailwind);
    vercelFiles.push({
      file: 'package.json',
      data: Buffer.from(JSON.stringify(packageJson, null, 2), 'utf8').toString('base64'),
      encoding: 'base64'
    });

    // 3. Generate Next.js configuration
    const nextConfig = this.generateNextConfig();
    vercelFiles.push({
      file: 'next.config.js',
      data: Buffer.from(nextConfig, 'utf8').toString('base64'),
      encoding: 'base64'
    });

    // 4. Generate TypeScript configuration
    const tsConfig = this.generateTsConfig();
    vercelFiles.push({
      file: 'tsconfig.json',
      data: Buffer.from(JSON.stringify(tsConfig, null, 2), 'utf8').toString('base64'),
      encoding: 'base64'
    });

    // 5. Generate Tailwind CSS configuration (if needed)
    if (needsTailwind) {
      const tailwindConfig = this.generateTailwindConfig();
      vercelFiles.push({
        file: 'tailwind.config.js',
        data: Buffer.from(tailwindConfig, 'utf8').toString('base64'),
        encoding: 'base64'
      });

      const tailwindCss = this.generateTailwindCss();
      vercelFiles.push({
        file: 'app/globals.css',
        data: Buffer.from(tailwindCss, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 6. Generate layout.tsx if not present
    if (!files['app/layout.tsx']) {
      const layout = this.generateLayout(needsTailwind);
      vercelFiles.push({
        file: 'app/layout.tsx',
        data: Buffer.from(layout, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 7. Generate .npmrc for dependency resolution
    const npmrc = this.generateNpmrc();
    vercelFiles.push({
      file: '.npmrc',
      data: Buffer.from(npmrc, 'utf8').toString('base64'),
      encoding: 'base64'
    });

    // 8. Generate .gitignore
    const gitignore = this.generateGitignore();
    vercelFiles.push({
      file: '.gitignore',
      data: Buffer.from(gitignore, 'utf8').toString('base64'),
      encoding: 'base64'
    });

    return vercelFiles;
  }

  private static generatePackageJson(
    projectName: string,
    userDependencies: Record<string, string>,
    userDevDependencies: Record<string, string>,
    needsTailwind: boolean
  ) {
    const baseDependencies = {
      'next': '15.3.4',
      'react': '^18.3.1',
      'react-dom': '^18.3.1',
      ...userDependencies
    };

    const baseDevDependencies = {
      '@types/node': '^20',
      '@types/react': '^18',
      '@types/react-dom': '^18',
      'typescript': '^5',
      ...userDevDependencies
    };

    // Add Tailwind if needed
    if (needsTailwind) {
      baseDevDependencies['tailwindcss'] = '^3.4.0';
      baseDevDependencies['autoprefixer'] = '^10.4.0';
      baseDevDependencies['postcss'] = '^8.4.0';
    }

    return {
      name: projectName,
      version: '0.1.0',
      private: true,
      scripts: {
        dev: 'next dev',
        build: 'next build',
        start: 'next start',
        lint: 'next lint'
      },
      dependencies: baseDependencies,
      devDependencies: baseDevDependencies,
      overrides: {
        "react": "^18.3.1",
        "react-dom": "^18.3.1"
      }
    };
  }

  private static generateNextConfig(): string {
    return `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['images.unsplash.com'],
  },
}

module.exports = nextConfig`;
  }

  private static generateTsConfig() {
    return {
      compilerOptions: {
        target: 'es5',
        lib: ['dom', 'dom.iterable', 'es6'],
        allowJs: true,
        skipLibCheck: true,
        strict: true,
        noEmit: true,
        esModuleInterop: true,
        module: 'esnext',
        moduleResolution: 'bundler',
        resolveJsonModule: true,
        isolatedModules: true,
        jsx: 'preserve',
        incremental: true,
        plugins: [
          {
            name: 'next'
          }
        ],
        baseUrl: '.',
        paths: {
          '@/*': ['./*']
        }
      },
      include: ['next-env.d.ts', '**/*.ts', '**/*.tsx', '.next/types/**/*.ts'],
      exclude: ['node_modules']
    };
  }

  private static needsTailwindFromFiles(files: Record<string, string>): boolean {
    // Check if any file contains Tailwind classes
    const tailwindPattern = /class(Name)?=["'][^"']*\b(bg-|text-|p-|m-|w-|h-|flex|grid|border|rounded)/;
    return Object.values(files).some(content => tailwindPattern.test(content));
  }

  private static generateTailwindConfig(): string {
    return `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`;
  }

  private static generateTailwindCss(): string {
    return `@tailwind base;
@tailwind components;
@tailwind utilities;`;
  }

  private static generateLayout(includeTailwind: boolean): string {
    const cssImport = includeTailwind ? "import './globals.css'" : '';
    
    return `import type { Metadata } from 'next'
${cssImport}

export const metadata: Metadata = {
  title: 'Generated App',
  description: 'Generated by AI',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}`;
  }

  private static generateNpmrc(): string {
    return `legacy-peer-deps=true
auto-install-peers=true`;
  }

  private static generateGitignore(): string {
    return `# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts`;
  }
}

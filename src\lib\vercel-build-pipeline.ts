import { VercelFile } from './vercel-deployment';

export interface BuildPipelineOptions {
  projectName: string;
  files: Record<string, string>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

export class VercelBuildPipeline {
  
  /**
   * Prepare files for Vercel deployment - AI generates all necessary files with fallbacks
   * This pipeline processes AI-generated files and adds essential files if missing
   */
  static prepareProjectFiles(options: BuildPipelineOptions): VercelFile[] {
    const { projectName, files, dependencies = {}, devDependencies = {} } = options;
    const vercelFiles: VercelFile[] = [];

    // Process all AI-generated files first
    Object.entries(files).forEach(([path, content]) => {
      vercelFiles.push({
        file: path,
        data: Buffer.from(content, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    });

    // Add essential files if AI didn't generate them (fallback mechanism)
    const existingFiles = new Set(Object.keys(files));

    // 1. Ensure package.json exists
    if (!existingFiles.has('package.json')) {
      console.log('AI did not generate package.json, creating fallback');
      const packageJson = this.generateFallbackPackageJson(projectName, dependencies, devDependencies, files);
      vercelFiles.push({
        file: 'package.json',
        data: Buffer.from(JSON.stringify(packageJson, null, 2), 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 2. Ensure .npmrc exists for dependency resolution
    if (!existingFiles.has('.npmrc')) {
      console.log('AI did not generate .npmrc, creating fallback');
      const npmrc = this.generateFallbackNpmrc();
      vercelFiles.push({
        file: '.npmrc',
        data: Buffer.from(npmrc, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    // 3. Ensure next.config.js exists
    if (!existingFiles.has('next.config.js')) {
      console.log('AI did not generate next.config.js, creating fallback');
      const nextConfig = this.generateFallbackNextConfig();
      vercelFiles.push({
        file: 'next.config.js',
        data: Buffer.from(nextConfig, 'utf8').toString('base64'),
        encoding: 'base64'
      });
    }

    return vercelFiles;
  }

  /**
   * Generate fallback package.json if AI didn't create one
   */
  private static generateFallbackPackageJson(
    projectName: string,
    userDependencies: Record<string, string>,
    userDevDependencies: Record<string, string>,
    files: Record<string, string>
  ) {
    // Detect if Tailwind is needed
    const needsTailwind = this.needsTailwindFromFiles(files);

    const baseDependencies = {
      'next': '15.3.4',
      'react': '18.2.0',
      'react-dom': '18.2.0',
      ...userDependencies
    };

    const baseDevDependencies: Record<string, string> = {
      '@types/node': '^20',
      '@types/react': '^18',
      '@types/react-dom': '^18',
      'typescript': '^5',
      ...userDevDependencies
    };

    // Add Tailwind if needed
    if (needsTailwind) {
      baseDevDependencies['tailwindcss'] = '^3.4.0';
      baseDevDependencies['autoprefixer'] = '^10.4.0';
      baseDevDependencies['postcss'] = '^8.4.0';
    }

    return {
      name: projectName,
      version: '0.1.0',
      private: true,
      scripts: {
        dev: 'next dev',
        build: 'next build',
        start: 'next start',
        lint: 'next lint'
      },
      dependencies: baseDependencies,
      devDependencies: baseDevDependencies,
      overrides: {
        "react": "18.2.0",
        "react-dom": "18.2.0"
      }
    };
  }

  /**
   * Generate fallback .npmrc if AI didn't create one
   */
  private static generateFallbackNpmrc(): string {
    return `legacy-peer-deps=true
auto-install-peers=true
strict-peer-deps=false
fund=false
audit=false`;
  }

  /**
   * Generate fallback next.config.js if AI didn't create one
   */
  private static generateFallbackNextConfig(): string {
    return `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['images.unsplash.com'],
  },
}

module.exports = nextConfig`;
  }

  /**
   * Check if Tailwind CSS is needed based on file contents
   */
  private static needsTailwindFromFiles(files: Record<string, string>): boolean {
    const tailwindPattern = /class(Name)?=["'][^"']*\b(bg-|text-|p-|m-|w-|h-|flex|grid|border|rounded)/;
    return Object.values(files).some(content => tailwindPattern.test(content));
  }
}
